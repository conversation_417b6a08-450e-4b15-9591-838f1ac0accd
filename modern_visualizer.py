#!/usr/bin/env python3
"""
Modern Synthesis Tree Visualizer using D3.js and Flask
Creates beautiful, interactive visualizations with modern web UI
"""

import json
import os
from flask import Flask, render_template, jsonify, request
from pymongo import Mongo<PERSON>lient
from typing import Dict, List, Any
import webbrowser
import threading
import time

app = Flask(__name__)

class ModernSynthesisVisualizer:
    """Modern web-based synthesis tree visualizer."""
    
    def __init__(self):
        self.db_client = None
        self.db = None
        self.collection = None
        
    def connect_to_mongodb(self):
        """Connect to MongoDB."""
        try:
            self.db_client = MongoClient('**************************************')
            self.db = self.db_client['retro_synthesis']
            self.collection = self.db['retro_data']
            return True
        except Exception as e:
            print(f"❌ MongoDB connection failed: {e}")
            return False
    
    def get_route_data(self, request_id: str) -> List[Dict]:
        """Fetch route data from MongoDB."""
        if self.collection is None:
            if not self.connect_to_mongodb():
                return []

        try:
            routes = list(self.collection.find({"request_id": request_id}))
            print(f"📊 Found {len(routes)} routes for request_id: {request_id}")
            return routes
        except Exception as e:
            print(f"❌ Error fetching data: {e}")
            return []
    
    def transform_data_for_d3(self, routes: List[Dict]) -> Dict:
        """Transform database data into D3.js compatible format with proper tree structure."""
        if not routes:
            return {"nodes": [], "links": []}

        nodes = []
        links = []
        node_id_map = {}

        # Get target from first route (all routes should have same target)
        first_route = routes[0]
        target_smiles = first_route.get('target_smiles', 'Unknown')

        # Add single target node
        target_node_id = "target"
        nodes.append({
            "id": target_node_id,
            "label": f"TARGET\n{target_smiles}",
            "type": "target",
            "smiles": target_smiles,
            "size": 40,
            "details": {
                "type": "Target Molecule",
                "smiles": target_smiles,
                "total_routes": len(routes),
                "description": f"Target molecule with {len(routes)} synthesis routes"
            }
        })
        node_id_map[target_node_id] = 0

        # Collect all reactions from all routes
        all_reactions = []
        for route in routes:
            route_id = route.get('route_id', 'unknown')
            route_reactions = route.get('data', [])

            # Add route info to each reaction
            for reaction in route_reactions:
                reaction['route_id'] = route_id
                reaction['route_score'] = route.get('total_route_score', 'N/A')
                all_reactions.append(reaction)

        print(f"📊 Processing {len(all_reactions)} total reactions from {len(routes)} routes")

        # Sort all reactions by route and step
        all_reactions.sort(key=lambda x: (x.get('route_id', 0), x.get('step', 0)))

        # Create a mapping of SMILES to the reactions that produce them
        smiles_to_producer = {}
        for reaction in all_reactions:
            reaction_string = reaction.get('reaction_string', '')
            if '>' in reaction_string:
                product = reaction_string.split('>')[-1].strip()
                smiles_to_producer[product] = reaction.get('reaction_id')

        # Add target to the mapping
        smiles_to_producer[target_smiles] = "target"

        for reaction in all_reactions:
            step = reaction.get('step', 0)
            reaction_id = reaction.get('reaction_id', 'unknown')
            reaction_string = reaction.get('reaction_string', 'N/A')
            parent_mol_id = reaction.get('parent_molecule_id')  # Tree parent (molecule node)
            parent_rxn_id = reaction.get('parent_reaction_id')  # Route parent (reaction dependency)

            # Add reaction node with unique ID
            route_id = reaction.get('route_id', 'unknown')
            reaction_node_id = f"rxn_{reaction_id}"

            # Only add if not already exists (avoid duplicates)
            if reaction_node_id not in node_id_map:
                nodes.append({
                    "id": reaction_node_id,
                    "label": f"R{route_id}-S{step}",
                    "type": "reaction",
                    "step": step,
                    "route_id": route_id,
                    "size": 30,
                    "details": {
                        "type": "Reaction",
                        "route_id": route_id,
                        "step": step,
                        "reaction_id": reaction_id,
                        "reaction_string": reaction_string,
                        "score": reaction.get('score', 'N/A'),
                        "route_score": reaction.get('route_score', 'N/A'),
                        "rxn_class": reaction.get('rxn_class', {}).get('reaction_name', 'N/A'),
                        "parent_molecule_id": parent_mol_id,
                        "parent_reaction_id": parent_rxn_id
                    }
                })
                node_id_map[reaction_node_id] = len(nodes) - 1

            # Connect based on parent_molecule_id (tree structure)
            if parent_mol_id:
                # Create parent molecule node if it doesn't exist
                parent_mol_node_id = f"mol_{parent_mol_id}"
                if parent_mol_node_id not in node_id_map:
                    nodes.append({
                        "id": parent_mol_node_id,
                        "label": f"Molecule\n{parent_mol_id[:12]}...",
                        "type": "intermediate",
                        "size": 25,
                        "details": {
                            "type": "Parent Molecule",
                            "molecule_id": parent_mol_id,
                            "description": "Molecule that this reaction synthesizes"
                        }
                    })
                    node_id_map[parent_mol_node_id] = len(nodes) - 1

                # Connect parent molecule to reaction (molecule needs this reaction)
                links.append({
                    "source": parent_mol_node_id,
                    "target": reaction_node_id,
                    "type": "synthesis",
                    "label": "needs synthesis"
                })
            else:
                # No parent molecule means this reaction produces the target
                links.append({
                    "source": target_node_id,
                    "target": reaction_node_id,
                    "type": "synthesis",
                    "label": "produces target"
                })

            # Add route dependency based on parent_reaction_id
            if parent_rxn_id:
                parent_rxn_node_id = f"rxn_{parent_rxn_id}"
                links.append({
                    "source": parent_rxn_node_id,
                    "target": reaction_node_id,
                    "type": "dependency",
                    "label": "enables"
                })
                
            # Add reactant nodes and connections
            reactants = reaction.get('reactants', [])
            for reactant in reactants:
                reactant_smiles = reactant.get('smiles', 'Unknown')
                is_terminal = reactant.get('is_terminal', 'UNKNOWN')

                # Create unique reactant node ID
                reactant_node_id = f"mol_{reactant_smiles.replace('.', '_').replace('(', '').replace(')', '')}"

                # Only add the node if it doesn't exist yet
                if reactant_node_id not in node_id_map:
                    nodes.append({
                        "id": reactant_node_id,
                        "label": reactant_smiles,
                        "type": "terminal" if is_terminal == "TERMINAL" else "intermediate",
                        "smiles": reactant_smiles,
                        "size": 25,
                        "details": {
                            "type": "Terminal Molecule" if is_terminal == "TERMINAL" else "Intermediate Molecule",
                            "smiles": reactant_smiles,
                            "name": reactant.get('name', 'N/A'),
                            "synthesis_score": reactant.get('synthesis_score', 'N/A'),
                            "status": is_terminal
                        }
                    })
                    node_id_map[reactant_node_id] = len(nodes) - 1

                # Connect reaction to reactant (reaction needs this reactant)
                links.append({
                    "source": reaction_node_id,
                    "target": reactant_node_id,
                    "type": "requires",
                    "label": "needs"
                })

                # If this reactant is produced by another reaction, connect them
                if reactant_smiles in smiles_to_producer:
                    producer_id = smiles_to_producer[reactant_smiles]
                    if producer_id != "target":  # Don't connect back to target
                        producer_node_id = f"rxn_{producer_id}"
                        # Check if this link doesn't already exist
                        existing_link = any(
                            link["source"] == producer_node_id and link["target"] == reactant_node_id
                            for link in links
                        )
                        if not existing_link:
                            links.append({
                                "source": producer_node_id,
                                "target": reactant_node_id,
                                "type": "produces",
                                "label": "synthesizes"
                            })

        print(f"✅ Created visualization with {len(nodes)} nodes and {len(links)} links")
        
        return {"nodes": nodes, "links": links}

# Global visualizer instance
visualizer = ModernSynthesisVisualizer()

@app.route('/')
def index():
    """Main page."""
    return render_template('index.html')

@app.route('/api/routes/<request_id>')
def get_routes(request_id):
    """API endpoint to get route data."""
    try:
        routes = visualizer.get_route_data(request_id)
        if not routes:
            return jsonify({
                "success": False,
                "error": f"No routes found for request_id: {request_id}",
                "data": {"nodes": [], "links": []},
                "metadata": {"request_id": request_id, "num_routes": 0}
            }), 404

        graph_data = visualizer.transform_data_for_d3(routes)
        return jsonify({
            "success": True,
            "data": graph_data,
            "metadata": {
                "request_id": request_id,
                "num_routes": len(routes),
                "target_smiles": routes[0].get('target_smiles', 'Unknown') if routes else 'Unknown'
            }
        })
    except Exception as e:
        print(f"❌ API Error: {e}")
        return jsonify({
            "success": False,
            "error": f"Server error: {str(e)}",
            "data": {"nodes": [], "links": []},
            "metadata": {"request_id": request_id, "num_routes": 0}
        }), 500

@app.route('/api/sample')
def get_sample_data():
    """Get sample data for testing."""
    sample_routes = [{
        "route_id": 1,
        "target_smiles": "Clc1cccnc1Cl",
        "total_route_score": 0.502830946,
        "num_steps": 2,
        "data": [
            {
                "step": 1,
                "parent_molecule_id": "2e4d97d2-ac12-40ac-85bb-5693328f3d1a",  # Target molecule ID
                "parent_reaction_id": None,  # First step has no parent reaction
                "reaction_id": "beb42a86-28d4-4adf-8cf0-792b0450ff0e",
                "reaction_string": "ClCl.Clc1ccccn1>ClC(Cl)Cl>Clc1cccnc1Cl",
                "score": 0.497,
                "rxn_class": {"reaction_name": "Chlorination"},
                "reactants": [
                    {"smiles": "ClCl", "name": "Chlorine", "is_terminal": "TERMINAL", "synthesis_score": 1.225},
                    {"smiles": "Clc1ccccn1", "name": "2-chloropyridine", "is_terminal": "UNSOLVED", "synthesis_score": 1.238}
                ]
            },
            {
                "step": 2,
                "parent_molecule_id": "9e173b91-6024-4812-92cb-fd212cc8644a",  # Intermediate molecule ID
                "parent_reaction_id": "beb42a86-28d4-4adf-8cf0-792b0450ff0e",  # Step 1 enables step 2
                "reaction_id": "4ea103d8-c978-423d-a5e2-c1076f8a6eb7",
                "reaction_string": "Clc1cccc(Cl)n1>C1CCOC1.[Al+3].[H-].[H-].[H-].[H-].[Li+]>Clc1ccccn1",
                "score": 0.508,
                "rxn_class": {"reaction_name": "Dechlorination"},
                "reactants": [
                    {"smiles": "Clc1cccc(Cl)n1", "name": "2,6-dichloropyridine", "is_terminal": "TERMINAL", "synthesis_score": 1.228}
                ]
            }
        ]
    }]
    
    graph_data = visualizer.transform_data_for_d3(sample_routes)
    return jsonify({
        "success": True,
        "data": graph_data,
        "metadata": {
            "request_id": "sample",
            "num_routes": 1,
            "target_smiles": "Clc1cccnc1Cl"
        }
    })

def create_templates():
    """Create HTML templates and static files."""
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)

    # Create the main HTML template
    create_index_template()
    create_css_files()
    create_js_files()

def create_index_template():
    """Create the main HTML template."""
    html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Modern Synthesis Tree Visualizer</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-flask"></i> Synthesis Tree Visualizer</h1>
                <p>Interactive visualization of retro synthesis routes</p>
            </div>
        </header>

        <!-- Controls Panel -->
        <div class="controls-panel">
            <div class="control-group">
                <label for="requestId">Request ID:</label>
                <input type="text" id="requestId" placeholder="Enter request ID (e.g., os6df8a82-9fae-479e-ba10-e92e0f8f9492)">
                <button id="loadBtn" class="btn btn-primary">
                    <i class="fas fa-search"></i> Load Routes
                </button>
                <button id="sampleBtn" class="btn btn-secondary">
                    <i class="fas fa-vial"></i> Load Sample
                </button>
            </div>

            <div class="control-group">
                <button id="resetBtn" class="btn btn-outline">
                    <i class="fas fa-redo"></i> Reset View
                </button>
                <button id="exportBtn" class="btn btn-outline">
                    <i class="fas fa-download"></i> Export PNG
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Info Panel -->
            <div class="info-panel" id="infoPanel">
                <div class="info-header">
                    <h3><i class="fas fa-info-circle"></i> Route Information</h3>
                    <button id="closeInfo" class="close-btn">&times;</button>
                </div>
                <div class="info-content" id="infoContent">
                    <p>Click on any node to see detailed information</p>
                </div>
            </div>

            <!-- Visualization Container -->
            <div class="viz-container">
                <div id="loading" class="loading">
                    <div class="spinner"></div>
                    <p>Loading synthesis routes...</p>
                </div>
                <div id="error" class="error" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p id="errorMessage">Error loading data</p>
                </div>
                <svg id="visualization"></svg>
            </div>
        </div>

        <!-- Legend -->
        <div class="legend">
            <h4><i class="fas fa-palette"></i> Legend</h4>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="legend-color target"></div>
                    <span>Target Molecule</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color reaction"></div>
                    <span>Reaction Step</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color terminal"></div>
                    <span>Terminal/Building Block</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color intermediate"></div>
                    <span>Intermediate Molecule</span>
                </div>
            </div>
            <div class="legend-edges">
                <div class="legend-item">
                    <div class="legend-line synthesis"></div>
                    <span>Synthesis Flow</span>
                </div>
                <div class="legend-item">
                    <div class="legend-line dependency"></div>
                    <span>Route Dependency</span>
                </div>
                <div class="legend-item">
                    <div class="legend-line produces"></div>
                    <span>Produces</span>
                </div>
            </div>
        </div>
    </div>

    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="{{ url_for('static', filename='js/visualizer.js') }}"></script>
</body>
</html>'''

    with open('templates/index.html', 'w') as f:
        f.write(html_content)

def create_css_files():
    """Create CSS files for styling."""
    css_content = '''/* Modern Synthesis Tree Visualizer Styles */
:root {
    --primary-color: #2563eb;
    --secondary-color: #7c3aed;
    --success-color: #059669;
    --warning-color: #d97706;
    --error-color: #dc2626;
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-dark: #1e293b;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--text-primary);
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    box-shadow: var(--shadow);
}

.header-content h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.header-content p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* Controls Panel */
.controls-panel {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.control-group label {
    font-weight: 500;
    color: var(--text-primary);
}

.control-group input {
    padding: 0.5rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.9rem;
    min-width: 300px;
    transition: all 0.2s ease;
}

.control-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Button Styles */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #6d28d9;
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-outline {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-outline:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    position: relative;
    overflow: hidden;
}

/* Info Panel */
.info-panel {
    width: 350px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-right: 1px solid var(--border-color);
    padding: 1.5rem;
    overflow-y: auto;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.info-panel.active {
    transform: translateX(0);
}

.info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.info-header h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0.25rem;
}

.close-btn:hover {
    color: var(--error-color);
}

/* Visualization Container */
.viz-container {
    flex: 1;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
}

#visualization {
    width: 100%;
    height: 100%;
}

/* Loading and Error States */
.loading, .error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    color: var(--error-color);
    background: rgba(255, 255, 255, 0.9);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
}

/* Legend */
.legend {
    position: absolute;
    bottom: 2rem;
    right: 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    min-width: 200px;
}

.legend h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.legend-items, .legend-edges {
    margin-bottom: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
}

.legend-color.target { background: #ff6b6b; }
.legend-color.reaction { background: #4ecdc4; }
.legend-color.terminal { background: #45b7d1; }
.legend-color.intermediate { background: #96ceb4; }

.legend-line {
    width: 20px;
    height: 3px;
    border-radius: 2px;
}

.legend-line.synthesis { background: #ff6b6b; }
.legend-line.dependency { background: #feca57; }
.legend-line.produces { background: #48dbfb; }

/* Responsive Design */
@media (max-width: 768px) {
    .controls-panel {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group input {
        min-width: auto;
    }

    .info-panel {
        width: 100%;
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        z-index: 1000;
    }

    .legend {
        position: relative;
        bottom: auto;
        right: auto;
        margin: 1rem;
    }
}

/* Node and Link Styles for D3 */
.node {
    cursor: pointer;
    transition: all 0.2s ease;
}

.node:hover {
    stroke-width: 3px;
}

.node.selected {
    stroke: var(--primary-color);
    stroke-width: 4px;
}

.link {
    fill: none;
    stroke-width: 2px;
    transition: all 0.2s ease;
}

.link:hover {
    stroke-width: 4px;
}

.node-label {
    font-family: 'Inter', sans-serif;
    font-size: 12px;
    font-weight: 500;
    text-anchor: middle;
    pointer-events: none;
    fill: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.75rem;
    border-radius: 8px;
    font-size: 0.85rem;
    pointer-events: none;
    z-index: 1000;
    max-width: 300px;
    box-shadow: var(--shadow-lg);
}'''

    with open('static/css/style.css', 'w') as f:
        f.write(css_content)

def create_js_files():
    """Create JavaScript files for D3.js visualization."""
    js_content = '''// Modern Synthesis Tree Visualizer - D3.js Implementation
class SynthesisVisualizer {
    constructor() {
        this.svg = null;
        this.simulation = null;
        this.nodes = [];
        this.links = [];
        this.width = 0;
        this.height = 0;
        this.tooltip = null;
        this.selectedNode = null;

        this.colors = {
            target: '#ff6b6b',
            reaction: '#4ecdc4',
            terminal: '#45b7d1',
            intermediate: '#96ceb4'
        };

        this.linkColors = {
            synthesis: '#ff6b6b',
            dependency: '#feca57',
            requires: '#74b9ff',
            produces: '#48dbfb'
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.createTooltip();
        this.setupSVG();
    }

    setupEventListeners() {
        document.getElementById('loadBtn').addEventListener('click', () => {
            const requestId = document.getElementById('requestId').value.trim();
            if (requestId) {
                this.loadRoutes(requestId);
            } else {
                this.showError('Please enter a request ID');
            }
        });

        document.getElementById('sampleBtn').addEventListener('click', () => {
            this.loadSampleData();
        });

        document.getElementById('resetBtn').addEventListener('click', () => {
            this.resetView();
        });

        document.getElementById('exportBtn').addEventListener('click', () => {
            this.exportPNG();
        });

        document.getElementById('closeInfo').addEventListener('click', () => {
            this.hideInfoPanel();
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    createTooltip() {
        this.tooltip = d3.select('body')
            .append('div')
            .attr('class', 'tooltip')
            .style('opacity', 0);
    }

    setupSVG() {
        const container = document.getElementById('visualization');
        this.width = container.clientWidth;
        this.height = container.clientHeight;

        this.svg = d3.select('#visualization')
            .attr('width', this.width)
            .attr('height', this.height);

        // Add zoom behavior
        const zoom = d3.zoom()
            .scaleExtent([0.1, 4])
            .on('zoom', (event) => {
                this.svg.select('.main-group')
                    .attr('transform', event.transform);
            });

        this.svg.call(zoom);

        // Create main group for zooming/panning
        this.svg.append('g').attr('class', 'main-group');
    }

    async loadRoutes(requestId) {
        this.showLoading();
        try {
            const response = await fetch(`/api/routes/${requestId}`);

            // Check if response is OK
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                const text = await response.text();
                throw new Error(`Expected JSON but got: ${text.substring(0, 100)}...`);
            }

            const data = await response.json();

            if (data.success) {
                if (data.data.nodes.length === 0) {
                    this.showError('No visualization data available for this request ID');
                } else {
                    this.renderVisualization(data.data, data.metadata);
                    this.hideLoading();
                }
            } else {
                this.showError(data.error || 'Failed to load routes');
            }
        } catch (error) {
            console.error('Load routes error:', error);
            this.showError('Network error: ' + error.message);
        }
    }

    async loadSampleData() {
        this.showLoading();
        try {
            const response = await fetch('/api/sample');

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                this.renderVisualization(data.data, data.metadata);
                this.hideLoading();
            } else {
                this.showError('Failed to load sample data: ' + (data.error || 'Unknown error'));
            }
        } catch (error) {
            console.error('Load sample error:', error);
            this.showError('Network error: ' + error.message);
        }
    }

    renderVisualization(graphData, metadata) {
        this.nodes = graphData.nodes;
        this.links = graphData.links;

        // Clear previous visualization
        this.svg.select('.main-group').selectAll('*').remove();

        // Create force simulation optimized for tree layout
        this.simulation = d3.forceSimulation(this.nodes)
            .force('link', d3.forceLink(this.links).id(d => d.id)
                .distance(d => {
                    // Different distances for different link types
                    if (d.type === 'synthesis') return 150;
                    if (d.type === 'dependency') return 200;
                    if (d.type === 'produces') return 120;
                    return 100;
                })
                .strength(0.8)
            )
            .force('charge', d3.forceManyBody()
                .strength(d => {
                    // Stronger repulsion for target and reactions
                    if (d.type === 'target') return -800;
                    if (d.type === 'reaction') return -600;
                    return -400;
                })
            )
            .force('center', d3.forceCenter(this.width / 2, this.height / 2))
            .force('collision', d3.forceCollide().radius(d => d.size + 10))
            .force('y', d3.forceY()
                .y(d => {
                    // Position nodes by type for better hierarchy
                    if (d.type === 'target') return this.height * 0.2;
                    if (d.type === 'reaction') return this.height * 0.5;
                    return this.height * 0.8;
                })
                .strength(0.3)
            );

        const mainGroup = this.svg.select('.main-group');

        // Create links
        const link = mainGroup.append('g')
            .attr('class', 'links')
            .selectAll('line')
            .data(this.links)
            .enter().append('line')
            .attr('class', 'link')
            .attr('stroke', d => this.linkColors[d.type] || '#999')
            .attr('stroke-width', 2)
            .attr('stroke-dasharray', d => d.type === 'dependency' ? '5,5' : null);

        // Create nodes
        const node = mainGroup.append('g')
            .attr('class', 'nodes')
            .selectAll('circle')
            .data(this.nodes)
            .enter().append('circle')
            .attr('class', 'node')
            .attr('r', d => d.size)
            .attr('fill', d => this.colors[d.type] || '#999')
            .attr('stroke', '#fff')
            .attr('stroke-width', 2)
            .call(this.drag());

        // Add labels
        const labels = mainGroup.append('g')
            .attr('class', 'labels')
            .selectAll('text')
            .data(this.nodes)
            .enter().append('text')
            .attr('class', 'node-label')
            .text(d => this.truncateLabel(d.label))
            .attr('dy', '.35em');

        // Add event listeners
        node
            .on('mouseover', (event, d) => this.showTooltip(event, d))
            .on('mouseout', () => this.hideTooltip())
            .on('click', (event, d) => this.selectNode(d));

        // Update positions on simulation tick
        this.simulation.on('tick', () => {
            link
                .attr('x1', d => d.source.x)
                .attr('y1', d => d.source.y)
                .attr('x2', d => d.target.x)
                .attr('y2', d => d.target.y);

            node
                .attr('cx', d => d.x)
                .attr('cy', d => d.y);

            labels
                .attr('x', d => d.x)
                .attr('y', d => d.y);
        });

        // Show metadata
        this.showMetadata(metadata);
    }

    drag() {
        return d3.drag()
            .on('start', (event, d) => {
                if (!event.active) this.simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            })
            .on('drag', (event, d) => {
                d.fx = event.x;
                d.fy = event.y;
            })
            .on('end', (event, d) => {
                if (!event.active) this.simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            });
    }

    showTooltip(event, d) {
        const details = d.details || {};
        let content = `<strong>${details.type || 'Node'}</strong><br>`;

        Object.entries(details).forEach(([key, value]) => {
            if (key !== 'type' && value !== 'N/A') {
                content += `<strong>${key}:</strong> ${value}<br>`;
            }
        });

        this.tooltip.transition()
            .duration(200)
            .style('opacity', .9);
        this.tooltip.html(content)
            .style('left', (event.pageX + 10) + 'px')
            .style('top', (event.pageY - 28) + 'px');
    }

    hideTooltip() {
        this.tooltip.transition()
            .duration(500)
            .style('opacity', 0);
    }

    selectNode(d) {
        // Remove previous selection
        this.svg.selectAll('.node').classed('selected', false);

        // Select current node
        this.svg.selectAll('.node')
            .filter(node => node.id === d.id)
            .classed('selected', true);

        this.selectedNode = d;
        this.showInfoPanel(d);
    }

    showInfoPanel(d) {
        const panel = document.getElementById('infoPanel');
        const content = document.getElementById('infoContent');

        let html = `<h4>${d.label}</h4>`;
        html += `<div class="info-details">`;

        Object.entries(d.details || {}).forEach(([key, value]) => {
            if (value !== 'N/A') {
                html += `<div class="info-item">
                    <strong>${key}:</strong> <span>${value}</span>
                </div>`;
            }
        });

        html += `</div>`;
        content.innerHTML = html;
        panel.classList.add('active');
    }

    hideInfoPanel() {
        document.getElementById('infoPanel').classList.remove('active');
        this.svg.selectAll('.node').classed('selected', false);
        this.selectedNode = null;
    }

    showMetadata(metadata) {
        console.log('Route metadata:', metadata);
        // You can display metadata in the UI if needed
    }

    truncateLabel(label, maxLength = 20) {
        return label.length > maxLength ? label.substring(0, maxLength) + '...' : label;
    }

    resetView() {
        if (this.svg) {
            this.svg.transition()
                .duration(750)
                .call(
                    d3.zoom().transform,
                    d3.zoomIdentity
                );
        }
    }

    exportPNG() {
        // Implementation for PNG export
        const svgElement = document.getElementById('visualization');
        const serializer = new XMLSerializer();
        const svgString = serializer.serializeToString(svgElement);

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();

        img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);

            const link = document.createElement('a');
            link.download = 'synthesis-tree.png';
            link.href = canvas.toDataURL();
            link.click();
        };

        img.src = 'data:image/svg+xml;base64,' + btoa(svgString);
    }

    showLoading() {
        document.getElementById('loading').style.display = 'block';
        document.getElementById('error').style.display = 'none';
    }

    hideLoading() {
        document.getElementById('loading').style.display = 'none';
    }

    showError(message) {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('error').style.display = 'block';
        document.getElementById('errorMessage').textContent = message;
    }

    handleResize() {
        const container = document.getElementById('visualization');
        this.width = container.clientWidth;
        this.height = container.clientHeight;

        this.svg
            .attr('width', this.width)
            .attr('height', this.height);

        if (this.simulation) {
            this.simulation
                .force('center', d3.forceCenter(this.width / 2, this.height / 2))
                .restart();
        }
    }
}

// Initialize the visualizer when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new SynthesisVisualizer();
});'''

    with open('static/js/visualizer.js', 'w') as f:
        f.write(js_content)

def open_browser():
    """Open browser after a delay."""
    time.sleep(1.5)
    webbrowser.open('http://localhost:5000')

def run_server(debug=False, open_browser_flag=True):
    """Run the Flask server."""
    create_templates()
    
    if open_browser_flag:
        threading.Thread(target=open_browser, daemon=True).start()
    
    print("🚀 Starting Modern Synthesis Visualizer...")
    print("🌐 Open http://localhost:5000 in your browser")
    print("📊 Features: Interactive D3.js graphs, beautiful UI, hover details")
    
    app.run(debug=debug, host='0.0.0.0', port=5000)

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Modern Synthesis Tree Visualizer')
    parser.add_argument('--debug', action='store_true', help='Run in debug mode')
    parser.add_argument('--no-browser', action='store_true', help='Don\'t open browser automatically')
    
    args = parser.parse_args()
    run_server(debug=args.debug, open_browser_flag=not args.no_browser)
