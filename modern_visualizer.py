#!/usr/bin/env python3
"""
Modern Synthesis Tree Visualizer using D3.js and Flask
Creates beautiful, interactive visualizations with modern web UI
"""

import json
import os
from flask import Flask, render_template, jsonify, request
from pymongo import Mongo<PERSON>lient
from typing import Dict, List, Any
import webbrowser
import threading
import time

app = Flask(__name__)

class ModernSynthesisVisualizer:
    """Modern web-based synthesis tree visualizer."""
    
    def __init__(self):
        self.db_client = None
        self.db = None
        self.collection = None
        
    def connect_to_mongodb(self):
        """Connect to MongoDB."""
        try:
            self.db_client = MongoClient('**************************************')
            self.db = self.db_client['retro_synthesis']
            self.collection = self.db['retro_data']
            return True
        except Exception as e:
            print(f"❌ MongoDB connection failed: {e}")
            return False
    
    def get_route_data(self, request_id: str) -> List[Dict]:
        """Fetch route data from MongoDB."""
        if not self.collection:
            if not self.connect_to_mongodb():
                return []
        
        try:
            routes = list(self.collection.find({"request_id": request_id}))
            return routes
        except Exception as e:
            print(f"❌ Error fetching data: {e}")
            return []
    
    def transform_data_for_d3(self, routes: List[Dict]) -> Dict:
        """Transform database data into D3.js compatible format."""
        if not routes:
            return {"nodes": [], "links": []}
        
        nodes = []
        links = []
        node_id_map = {}
        
        for route in routes:
            route_id = route.get('route_id', 1)
            target_smiles = route.get('target_smiles', 'Unknown')
            
            # Add target node
            target_node_id = f"target_{route_id}"
            if target_node_id not in node_id_map:
                nodes.append({
                    "id": target_node_id,
                    "label": f"TARGET\n{target_smiles}",
                    "type": "target",
                    "smiles": target_smiles,
                    "route_id": route_id,
                    "size": 40,
                    "details": {
                        "type": "Target Molecule",
                        "smiles": target_smiles,
                        "route_id": route_id,
                        "total_score": route.get('total_route_score', 'N/A'),
                        "num_steps": route.get('num_steps', 'N/A')
                    }
                })
                node_id_map[target_node_id] = len(nodes) - 1
            
            # Process reactions
            reactions = route.get('data', [])
            for reaction in reactions:
                step = reaction.get('step', 0)
                reaction_id = reaction.get('reaction_id', 'unknown')
                reaction_string = reaction.get('reaction_string', 'N/A')
                parent_rxn_id = reaction.get('parent_reaction_id')
                
                # Add reaction node
                reaction_node_id = f"rxn_{reaction_id}"
                if reaction_node_id not in node_id_map:
                    nodes.append({
                        "id": reaction_node_id,
                        "label": f"Step {step}",
                        "type": "reaction",
                        "step": step,
                        "size": 30,
                        "details": {
                            "type": "Reaction",
                            "step": step,
                            "reaction_id": reaction_id,
                            "reaction_string": reaction_string,
                            "score": reaction.get('score', 'N/A'),
                            "rxn_class": reaction.get('rxn_class', {}).get('reaction_name', 'N/A')
                        }
                    })
                    node_id_map[reaction_node_id] = len(nodes) - 1
                
                # Connect target to first step
                if step == 1:
                    links.append({
                        "source": target_node_id,
                        "target": reaction_node_id,
                        "type": "synthesis",
                        "label": "produces"
                    })
                
                # Add route dependency
                if parent_rxn_id:
                    parent_node_id = f"rxn_{parent_rxn_id}"
                    links.append({
                        "source": parent_node_id,
                        "target": reaction_node_id,
                        "type": "dependency",
                        "label": "enables"
                    })
                
                # Add reactant nodes
                reactants = reaction.get('reactants', [])
                for i, reactant in enumerate(reactants):
                    reactant_smiles = reactant.get('smiles', 'Unknown')
                    is_terminal = reactant.get('is_terminal', 'UNKNOWN')
                    
                    reactant_node_id = f"mol_{reaction_id}_{i}"
                    nodes.append({
                        "id": reactant_node_id,
                        "label": reactant_smiles,
                        "type": "terminal" if is_terminal == "TERMINAL" else "intermediate",
                        "smiles": reactant_smiles,
                        "size": 25,
                        "details": {
                            "type": "Terminal Molecule" if is_terminal == "TERMINAL" else "Intermediate Molecule",
                            "smiles": reactant_smiles,
                            "name": reactant.get('name', 'N/A'),
                            "synthesis_score": reactant.get('synthesis_score', 'N/A'),
                            "status": is_terminal
                        }
                    })
                    
                    # Connect reaction to reactant
                    links.append({
                        "source": reaction_node_id,
                        "target": reactant_node_id,
                        "type": "requires",
                        "label": "needs"
                    })
                    
                    # Check if this reactant is produced by another reaction
                    for other_reaction in reactions:
                        if other_reaction['reaction_id'] != reaction['reaction_id']:
                            other_rxn_string = other_reaction.get('reaction_string', '')
                            if '>' in other_rxn_string:
                                product = other_rxn_string.split('>')[-1].strip()
                                if product == reactant_smiles:
                                    other_rxn_node_id = f"rxn_{other_reaction['reaction_id']}"
                                    links.append({
                                        "source": other_rxn_node_id,
                                        "target": reactant_node_id,
                                        "type": "produces",
                                        "label": "synthesizes"
                                    })
        
        return {"nodes": nodes, "links": links}

# Global visualizer instance
visualizer = ModernSynthesisVisualizer()

@app.route('/')
def index():
    """Main page."""
    return render_template('index.html')

@app.route('/api/routes/<request_id>')
def get_routes(request_id):
    """API endpoint to get route data."""
    routes = visualizer.get_route_data(request_id)
    if not routes:
        return jsonify({"error": "No routes found"}), 404
    
    graph_data = visualizer.transform_data_for_d3(routes)
    return jsonify({
        "success": True,
        "data": graph_data,
        "metadata": {
            "request_id": request_id,
            "num_routes": len(routes),
            "target_smiles": routes[0].get('target_smiles', 'Unknown') if routes else 'Unknown'
        }
    })

@app.route('/api/sample')
def get_sample_data():
    """Get sample data for testing."""
    sample_routes = [{
        "route_id": 1,
        "target_smiles": "Clc1cccnc1Cl",
        "total_route_score": 0.502830946,
        "num_steps": 2,
        "data": [
            {
                "step": 1,
                "parent_reaction_id": None,
                "reaction_id": "beb42a86-28d4-4adf-8cf0-792b0450ff0e",
                "reaction_string": "ClCl.Clc1ccccn1>ClC(Cl)Cl>Clc1cccnc1Cl",
                "score": 0.497,
                "rxn_class": {"reaction_name": "Chlorination"},
                "reactants": [
                    {"smiles": "ClCl", "name": "Chlorine", "is_terminal": "TERMINAL", "synthesis_score": 1.225},
                    {"smiles": "Clc1ccccn1", "name": "2-chloropyridine", "is_terminal": "UNSOLVED", "synthesis_score": 1.238}
                ]
            },
            {
                "step": 2,
                "parent_reaction_id": "beb42a86-28d4-4adf-8cf0-792b0450ff0e",
                "reaction_id": "4ea103d8-c978-423d-a5e2-c1076f8a6eb7",
                "reaction_string": "Clc1cccc(Cl)n1>C1CCOC1.[Al+3].[H-].[H-].[H-].[H-].[Li+]>Clc1ccccn1",
                "score": 0.508,
                "rxn_class": {"reaction_name": "Dechlorination"},
                "reactants": [
                    {"smiles": "Clc1cccc(Cl)n1", "name": "2,6-dichloropyridine", "is_terminal": "TERMINAL", "synthesis_score": 1.228}
                ]
            }
        ]
    }]
    
    graph_data = visualizer.transform_data_for_d3(sample_routes)
    return jsonify({
        "success": True,
        "data": graph_data,
        "metadata": {
            "request_id": "sample",
            "num_routes": 1,
            "target_smiles": "Clc1cccnc1Cl"
        }
    })

def create_templates():
    """Create HTML templates."""
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)

def open_browser():
    """Open browser after a delay."""
    time.sleep(1.5)
    webbrowser.open('http://localhost:5000')

def run_server(debug=False, open_browser_flag=True):
    """Run the Flask server."""
    create_templates()
    
    if open_browser_flag:
        threading.Thread(target=open_browser, daemon=True).start()
    
    print("🚀 Starting Modern Synthesis Visualizer...")
    print("🌐 Open http://localhost:5000 in your browser")
    print("📊 Features: Interactive D3.js graphs, beautiful UI, hover details")
    
    app.run(debug=debug, host='0.0.0.0', port=5000)

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Modern Synthesis Tree Visualizer')
    parser.add_argument('--debug', action='store_true', help='Run in debug mode')
    parser.add_argument('--no-browser', action='store_true', help='Don\'t open browser automatically')
    
    args = parser.parse_args()
    run_server(debug=args.debug, open_browser_flag=not args.no_browser)
