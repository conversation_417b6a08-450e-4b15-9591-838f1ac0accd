FROM python:3.8.16

ENV DEBIAN_FRONTEND=noninteractive
ENV CONDA_DIR=/opt/conda
ENV PATH=$CONDA_DIR/bin:$PATH

WORKDIR /app

# ----------------------------
# Install system dependencies
# ----------------------------
RUN apt-get update && apt-get install -y \
    wget \
    git \
    build-essential \
    unzip \
    curl \
    software-properties-common \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
 && rm -rf /var/lib/apt/lists/*

# ----------------------------
# Install Miniconda (Python 3.10 compatible)
# ----------------------------
RUN wget https://repo.anaconda.com/miniconda/Miniconda3-py310_24.1.2-0-Linux-x86_64.sh -O miniconda.sh && \
    bash miniconda.sh -b -p $CONDA_DIR && \
    rm miniconda.sh && \
    conda clean -ay

#----------------------------
# Clone Parrot repo and merge local files
# ----------------------------
RUN git clone https://github.com/wangxr0526/Parrot.git /opt/Parrot


WORKDIR /opt/Parrot
RUN conda env create -f envs.yaml

# Activate parrot_env, install extras, download data
RUN $CONDA_DIR/envs/parrot_env/bin/pip install gdown wtforms flask flask_bootstrap && \
    $CONDA_DIR/envs/parrot_env/bin/python preprocess_script/download_data.py

# ----------------------------
# Set up ltr_runner Conda env (Python 3.10)
# ----------------------------
# Step 1: Create the conda environment first
RUN conda create -y -n ltr_runner_env python=3.10

# Step 2: Copy the source code after env creation
COPY ./ltr_runner /opt/ltr_runner

# Step 3: Install the requirements using pip from the new environment
RUN /opt/conda/envs/ltr_runner_env/bin/pip install --no-cache-dir -r /opt/ltr_runner/requirements.txt

COPY ./parrot/ /opt/Parrot/ 
# ----------------------------
# Set up your main app
# ----------------------------
WORKDIR /app
COPY . .

# Unzip assets
RUN unzip assets.zip -d temp && \
    mkdir -p assets && \
    cp -r temp/assets/* ./assets/ && \
    rm -rf temp assets.zip

RUN pip install --no-cache-dir -r requirements.txt
RUN mkdir -p /app/output
RUN sed -i 's/\r$//' start.sh && chmod +x start.sh

RUN $CONDA_DIR/envs/parrot_env/bin/pip install gunicorn 

EXPOSE 8030
EXPOSE 5050

CMD ["/bin/bash", "./start.sh"]