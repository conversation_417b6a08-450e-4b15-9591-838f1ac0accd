#!/bin/bash

/opt/conda/envs/parrot_env/bin/gunicorn --workers 1 --timeout 600 --bind 0.0.0.0:5050 --chdir /opt/Parrot parrot_service:app &

# Start Celery worker in the background
celery -A main worker --loglevel=INFO --concurrency=1 --pool=prefork --hostname=retro@%h &

celery -A enrich_worker worker --loglevel=INFO --concurrency=5 --pool=prefork --hostname=enrichment@%h &

# Run health checks
python3 health_checks.py

# Keep script running
wait