import os
import json, uuid
from config.settings import Config
from celery import Celery

# Initialize Celery app (same config as your worker)
app_config = Config()
sentinel_transport_options = {}
sentinel_hosts = []
if app_config.REDIS_SENTINEL_URL:
    sentinal_str = app_config.REDIS_SENTINEL_URL.replace('/0', '').split("sentinel://")
    sentinel_hosts = [(sentinal_str[1].split(":")[0], int(sentinal_str[1].split(":")[1]))]
    app_config.REDIS_URL = app_config.REDIS_SENTINEL_URL
    sentinel_transport_options = {
        'master_name': app_config.REDIS_SENTINEL_SERVICE_NAME,
        'sentinels': sentinel_hosts,
        'sentinel_kwargs': {
            'socket_timeout': 5,
            'socket_connect_timeout': 5,
        },
        'socket_keepalive': True,
        'retry_on_timeout': True,
        'health_check_interval': 30,
    }



celery_infow_app = Celery(__name__, broker=app_config.REDIS_URL, backend=app_config.REDIS_URL)
celery_infow_app.conf.update(
    broker_transport_options=sentinel_transport_options,
    result_backend_transport_options=sentinel_transport_options,
)
def send_enrich_inflow_task(payload):
    """Send a task to the patent worker"""
    INPUT_REDIS_QUEUE = os.getenv("ENRICH_INPUT_REDIS_QUEUE", "enrich_input_job_queue")
    
    # Send task to specific queue
    result = celery_infow_app.send_task(
        'process_enrich_task',
        args=[payload],
        queue=INPUT_REDIS_QUEUE
    )
    return result
