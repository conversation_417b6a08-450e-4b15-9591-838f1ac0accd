import re
from typing import List, Dict, Any, <PERSON><PERSON>

def detect_hallucinated_reagents(reaction_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Detect and remove hallucinated reagents from reaction data.
    
    Args:
        reaction_data: Dictionary containing reaction information with 'reagents' field
        
    Returns:
        Dictionary with cleaned reagents and metadata about what was removed
    """
    
    reagents_string = reaction_data.get('reagents', '')
    
    if not reagents_string:
        return {
            'cleaned_reagents': '',
            'removed_reagents': [],
            'removal_reasons': [],
            'original_count': 0,
            'cleaned_count': 0
        }
    
    # Split reagents by '.' separator
    reagent_list = reagents_string.split('.')
    
    cleaned_reagents = []
    removed_reagents = []
    removal_reasons = []
    
    for reagent in reagent_list:
        reagent = reagent.strip()
        if not reagent:
            continue
            
        is_hallucinated, reason = is_reagent_hallucinated(reagent)
        
        if is_hallucinated:
            removed_reagents.append(reagent)
            removal_reasons.append(reason)
        else:
            cleaned_reagents.append(reagent)
    
    # Create cleaned reaction data
    cleaned_reaction = reaction_data.copy()
    cleaned_reaction['reagents'] = '.'.join(cleaned_reagents)
    
    return {
        'cleaned_reaction': cleaned_reaction,
        'cleaned_reagents': '.'.join(cleaned_reagents),
        'removed_reagents': removed_reagents,
        'removal_reasons': removal_reasons,
        'original_count': len(reagent_list),
        'cleaned_count': len(cleaned_reagents)
    }

def is_reagent_hallucinated(reagent: str) -> Tuple[bool, str]:
    """
    Determine if a reagent is likely hallucinated based on various criteria.
    
    Args:
        reagent: SMILES string of the reagent
        
    Returns:
        Tuple of (is_hallucinated: bool, reason: str)
    """
    
    # Check 1: Extremely long carbon chains (likely hallucinated)
    if len(reagent) > 200:  # Adjust threshold as needed
        return True, "Extremely long SMILES string (>200 characters)"
    
    # Check 2: Excessive repetition of 'C' (long alkyl chains)
    consecutive_c_count = len(max(re.findall(r'C+', reagent), key=len, default=''))
    if consecutive_c_count > 50:  # Adjust threshold as needed
        return True, f"Excessive consecutive carbons ({consecutive_c_count})"
    
    # Check 3: Very high carbon to heteroatom ratio
    carbon_count = reagent.count('C')
    heteroatom_count = sum(reagent.count(atom) for atom in ['N', 'O', 'S', 'P', 'F', 'Cl', 'Br', 'I'])
    
    if carbon_count > 100 and heteroatom_count == 0:
        return True, "Pure hydrocarbon chain with excessive length"
    
    # Check 4: Unrealistic molecular weight estimation
    # Simple approximation: each C ≈ 12, each H ≈ 1, typical MW should be reasonable
    if carbon_count > 80:  # Approximate MW > 1000 Da
        return True, f"Likely excessive molecular weight (C count: {carbon_count})"
    
    # Check 5: Repetitive patterns that suggest generation artifacts
    if has_repetitive_pattern(reagent):
        return True, "Contains repetitive patterns suggesting generation artifact"
    
    # Check 6: Invalid SMILES characters or patterns
    if not is_valid_smiles_pattern(reagent):
        return True, "Contains invalid SMILES patterns"
    
    return False, ""

def has_repetitive_pattern(smiles: str) -> bool:
    """
    Detect if SMILES contains repetitive patterns suggesting hallucination.
    """
    # Look for patterns that repeat more than what's chemically reasonable
    for pattern_length in [3, 4, 5, 6]:
        for i in range(len(smiles) - pattern_length * 10):
            pattern = smiles[i:i + pattern_length]
            if pattern in smiles[i + pattern_length:i + pattern_length * 10]:
                # Check if pattern repeats too many times
                repeats = smiles.count(pattern)
                if repeats > 20:  # Adjust threshold as needed
                    return True
    return False

def is_valid_smiles_pattern(smiles: str) -> bool:
    """
    Basic validation of SMILES patterns.
    """
    # Check for balanced brackets
    bracket_pairs = {'(': ')', '[': ']', '{': '}'}
    stack = []
    
    for char in smiles:
        if char in bracket_pairs:
            stack.append(char)
        elif char in bracket_pairs.values():
            if not stack:
                return False
            if bracket_pairs[stack[-1]] != char:
                return False
            stack.pop()
    
    return len(stack) == 0

def batch_clean_reactions(reactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Clean multiple reactions and return summary statistics.
    """
    cleaned_reactions = []
    total_removed = 0
    
    for reaction in reactions:
        result = detect_hallucinated_reagents(reaction)
        cleaned_reactions.append(result['cleaned_reaction'])
        total_removed += len(result['removed_reagents'])
        
        # Print summary for each reaction
        if result['removed_reagents']:
            print(f"Reaction {reaction.get('reaction_id', 'Unknown')}:")
            print(f"  Removed {len(result['removed_reagents'])} reagents")
            for reagent, reason in zip(result['removed_reagents'], result['removal_reasons']):
                print(f"    - {reagent[:50]}{'...' if len(reagent) > 50 else ''} ({reason})")
    
    print(f"\nTotal reagents removed across all reactions: {total_removed}")
    return cleaned_reactions

# Pandas apply function
def clean_reagents_pandas(reagents_string: str) -> str:
    """
    Pandas apply function to clean reagents column.
    
    Args:
        reagents_string: String containing reagents separated by '.'
        
    Returns:
        Cleaned reagents string with hallucinated reagents removed
    """
    if not reagents_string or reagents_string.strip() == '':
        return ''
    
    # Split reagents by '.' separator
    reagent_list = reagents_string.split('.')
    cleaned_reagents = []
    
    for reagent in reagent_list:
        reagent = reagent.strip()
        if not reagent:
            continue
            
        is_hallucinated, _ = is_reagent_hallucinated(reagent)
        
        if not is_hallucinated:
            cleaned_reagents.append(reagent)
    
    return '.'.join(cleaned_reagents)
