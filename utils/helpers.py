import contextvars
from rdkit import Chem

# Define context variables at module level
REQUEST_ID = contextvars.ContextVar('REQUEST_ID', default=None)

def neutralize_smi(smiles: str) -> str:    # from: https://www.rdkit.org/docs/Cookbook.html#neutralizing-molecules
    if '-' in smiles or '+' in smiles:
        try:
            mol = Chem.MolFromSmiles(smiles)
            pattern = Chem.MolFromSmarts("[+1!h0!$([*]~[-1,-2,-3,-4]),-1!$([*]~[+1,+2,+3,+4])]")
            at_matches = mol.GetSubstructMatches(pattern)
            at_matches_list = [y[0] for y in at_matches]
            if len(at_matches_list) > 0:
                for at_idx in at_matches_list:
                    atom = mol.GetAtomWithIdx(at_idx)
                    chg = atom.GetFormalCharge()
                    hcount = atom.GetTotalNumHs()
                    atom.SetFormalCharge(0)
                    atom.SetNumExplicitHs(hcount - chg)
                    atom.UpdatePropertyCache()
            return Chem.MolToSmiles(mol)
        except:
            return smiles
    else:
        return smiles

def canonicalize_smiles(smiles: str) -> str:
    '''
    Molecule canonicalization that does not change the SMILES order of molecules in case of multiple molecules.
    Also neutralizes any charge of the molecules.
    Args:
    smiles (str): SMILES string of the molecule(s).
    Returns:
    str: Canonicalized SMILES string of the molecule(s).
    '''
    returned = []
    any_error = False
    for molecule in smiles.split('.'):
        molecule = neutralize_smi(molecule)
        mol = Chem.MolFromSmiles(molecule)
        if mol is not None:
            returned.append(Chem.MolToSmiles(mol, isomericSmiles=True, canonical=True))
        else:
            any_error = True
    if not any_error:
        return '.'.join(returned)
    else:
        return smiles