#!/usr/bin/env python3
"""
Interactive Web-based Synthesis Tree Visualizer

This creates an interactive HTML interface for visualizing synthesis trees
with zoom, pan, and hover capabilities.
"""

import json
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional
from pymongo import MongoClient
import networkx as nx
from pyvis.network import Network

class InteractiveSynthesisVisualizer:
    """Creates interactive HTML visualizations of synthesis trees."""
    
    def __init__(self):
        self.net = None
        self.node_colors = {
            'target': '#FF9800',      # Orange
            'molecule': '#2196F3',    # Blue  
            'reaction': '#FFC107',    # Amber
            'terminal': '#4CAF50'     # Green
        }
        self.edge_colors = {
            'tree_parent': '#1976D2',     # Dark blue
            'route_parent': '#D32F2F',    # Red
            'produces': '#757575'         # Gray
        }
    
    def create_network(self, title: str = "Synthesis Tree") -> Network:
        """Create a new pyvis network."""
        self.net = Network(
            height="800px",
            width="100%",
            bgcolor="#ffffff",
            font_color="black",
            directed=True
        )
        
        # Configure physics for better layout
        self.net.set_options("""
        var options = {
          "physics": {
            "enabled": true,
            "hierarchicalRepulsion": {
              "centralGravity": 0.3,
              "springLength": 100,
              "springConstant": 0.01,
              "nodeDistance": 120,
              "damping": 0.09
            },
            "maxVelocity": 50,
            "solver": "hierarchicalRepulsion",
            "timestep": 0.35,
            "stabilization": {"iterations": 150}
          },
          "layout": {
            "hierarchical": {
              "enabled": true,
              "levelSeparation": 150,
              "nodeSpacing": 100,
              "treeSpacing": 200,
              "blockShifting": true,
              "edgeMinimization": true,
              "parentCentralization": true,
              "direction": "UD",
              "sortMethod": "directed"
            }
          }
        }
        """)
        
        return self.net
    
    def visualize_from_db_data(self, db_data: Dict[str, Any], output_path: str = "synthesis_tree.html"):
        """
        Create interactive visualization from database route data.
        
        Args:
            db_data: Database document containing route data
            output_path: Output HTML file path
        """
        title = f"Route {db_data.get('route_id', 'Unknown')} - {db_data.get('target_smiles', 'Unknown')}"
        self.create_network(title)
        
        # Add target molecule node
        target_smiles = db_data.get('target_smiles', 'Unknown')
        target_info = f"""
        <b>TARGET MOLECULE</b><br>
        SMILES: {target_smiles}<br>
        Route ID: {db_data.get('route_id', 'N/A')}<br>
        Steps: {db_data.get('num_steps', 'N/A')}<br>
        Score: {db_data.get('total_route_score', 'N/A')}
        """
        
        self.net.add_node(
            'target',
            label=f"TARGET\n{target_smiles}",
            title=target_info,
            color=self.node_colors['target'],
            shape='ellipse',
            size=30,
            font={'size': 14, 'color': 'white'}
        )
        
        # Process reactions
        reactions = db_data.get('data', [])
        molecule_nodes = set()
        
        for reaction in reactions:
            step = reaction.get('step', 0)
            reaction_id = reaction.get('reaction_id', 'unknown')
            parent_mol_id = reaction.get('parent_molecule_id') or reaction.get('parent')
            parent_rxn_id = reaction.get('parent_reaction_id')
            reaction_string = reaction.get('reaction_string', 'N/A')
            retro_smiles = reaction.get('retro_smiles', 'N/A')
            score = reaction.get('score', 'N/A')
            
            # Create reaction info popup
            reaction_info = f"""
            <b>REACTION STEP {step}</b><br>
            ID: {reaction_id}<br>
            Reaction: {reaction_string}<br>
            Retro SMILES: {retro_smiles}<br>
            Score: {score}<br>
            Parent Molecule: {parent_mol_id or 'None'}<br>
            Parent Reaction: {parent_rxn_id or 'None'}
            """
            
            # Add reaction node
            self.net.add_node(
                reaction_id,
                label=f"STEP {step}\n{reaction_string[:30]}...",
                title=reaction_info,
                color=self.node_colors['reaction'],
                shape='box',
                size=25,
                font={'size': 12}
            )
            
            # Add edge from parent molecule to reaction
            if parent_mol_id and parent_mol_id != 'target':
                if parent_mol_id not in molecule_nodes:
                    mol_info = f"""
                    <b>INTERMEDIATE MOLECULE</b><br>
                    ID: {parent_mol_id}<br>
                    Type: Intermediate
                    """
                    self.net.add_node(
                        parent_mol_id,
                        label=f"MOLECULE\n{parent_mol_id[:12]}...",
                        title=mol_info,
                        color=self.node_colors['molecule'],
                        shape='ellipse',
                        size=20,
                        font={'size': 10}
                    )
                    molecule_nodes.add(parent_mol_id)
                
                self.net.add_edge(
                    parent_mol_id,
                    reaction_id,
                    color=self.edge_colors['tree_parent'],
                    label='synthesizes',
                    width=2
                )
            else:
                # Connect to target
                self.net.add_edge(
                    'target',
                    reaction_id,
                    color=self.edge_colors['tree_parent'],
                    label='from target',
                    width=2
                )
            
            # Add route dependency edge
            if parent_rxn_id:
                self.net.add_edge(
                    parent_rxn_id,
                    reaction_id,
                    color=self.edge_colors['route_parent'],
                    label='depends on',
                    width=3,
                    dashes=True
                )
            
            # Add reactant nodes
            reactants = reaction.get('reactants', [])
            for i, reactant in enumerate(reactants):
                reactant_smiles = reactant.get('smiles', 'Unknown')
                is_terminal = reactant.get('is_terminal', 'UNKNOWN')
                synthesis_score = reactant.get('synthesis_score', 'N/A')
                name = reactant.get('name', 'N/A')
                
                reactant_id = f"{reaction_id}_reactant_{i}"
                color = self.node_colors['terminal'] if is_terminal == 'TERMINAL' else self.node_colors['molecule']
                
                reactant_info = f"""
                <b>{'TERMINAL MOLECULE' if is_terminal == 'TERMINAL' else 'REACTANT MOLECULE'}</b><br>
                SMILES: {reactant_smiles}<br>
                Name: {name}<br>
                Synthesis Score: {synthesis_score}<br>
                Status: {is_terminal}
                """
                
                self.net.add_node(
                    reactant_id,
                    label=f"{'TERMINAL' if is_terminal == 'TERMINAL' else 'REACTANT'}\n{reactant_smiles}",
                    title=reactant_info,
                    color=color,
                    shape='ellipse',
                    size=18,
                    font={'size': 10}
                )
                
                # Edge from reaction to reactant
                self.net.add_edge(
                    reaction_id,
                    reactant_id,
                    color=self.edge_colors['produces'],
                    label='produces',
                    width=1
                )
        
        # Save the HTML file
        try:
            self.net.save_graph(output_path)
            print(f"✅ Interactive visualization saved as {output_path}")
            print(f"🌐 Open {output_path} in your web browser to view")
            return output_path
        except Exception as e:
            print(f"❌ Error saving visualization: {e}")
            return None
    
    def visualize_multiple_routes(self, routes_data: List[Dict], output_path: str = "multiple_routes.html"):
        """
        Create visualization with multiple routes for comparison.
        
        Args:
            routes_data: List of database documents
            output_path: Output HTML file path
        """
        self.create_network("Multiple Synthesis Routes Comparison")
        
        colors = ['#FF5722', '#9C27B0', '#3F51B5', '#009688', '#FF9800']
        
        for route_idx, db_data in enumerate(routes_data):
            route_color = colors[route_idx % len(colors)]
            target_smiles = db_data.get('target_smiles', 'Unknown')
            route_id = db_data.get('route_id', route_idx + 1)
            
            # Add target with route-specific color
            target_node_id = f"target_{route_id}"
            self.net.add_node(
                target_node_id,
                label=f"TARGET {route_id}\n{target_smiles}",
                title=f"Route {route_id} Target: {target_smiles}",
                color=route_color,
                shape='ellipse',
                size=25
            )
            
            # Process reactions for this route
            reactions = db_data.get('data', [])
            for reaction in reactions:
                reaction_id = f"route_{route_id}_{reaction.get('reaction_id', 'unknown')}"
                step = reaction.get('step', 0)
                
                self.net.add_node(
                    reaction_id,
                    label=f"R{route_id}-S{step}",
                    title=f"Route {route_id}, Step {step}: {reaction.get('reaction_string', 'N/A')}",
                    color=route_color,
                    shape='box',
                    size=20
                )
                
                self.net.add_edge(target_node_id, reaction_id, color=route_color)
        
        try:
            self.net.save_graph(output_path)
            print(f"✅ Multi-route visualization saved as {output_path}")
            return output_path
        except Exception as e:
            print(f"❌ Error saving multi-route visualization: {e}")
            return None

def fetch_and_visualize_from_mongodb(request_id: str, output_path: str = "mongodb_routes.html"):
    """Fetch data from MongoDB and create visualization."""
    try:
        client = MongoClient('**************************************')
        db = client['retro_synthesis']
        collection = db['retro_data']
        
        # Fetch routes for the request
        routes = list(collection.find({"request_id": request_id}))
        
        if not routes:
            print(f"❌ No routes found for request_id: {request_id}")
            return None
        
        print(f"📊 Found {len(routes)} routes for request_id: {request_id}")
        
        visualizer = InteractiveSynthesisVisualizer()
        
        if len(routes) == 1:
            # Single route visualization
            result = visualizer.visualize_from_db_data(routes[0], output_path)
        else:
            # Multiple routes comparison
            result = visualizer.visualize_multiple_routes(routes, output_path)
        
        return result
        
    except Exception as e:
        print(f"❌ Error connecting to MongoDB: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description='Interactive Synthesis Tree Visualizer')
    parser.add_argument('--request-id', type=str, help='MongoDB request ID to visualize')
    parser.add_argument('--json-file', type=str, help='JSON file containing route data')
    parser.add_argument('--output', type=str, default='synthesis_tree.html', help='Output HTML file')
    
    args = parser.parse_args()
    
    if args.request_id:
        result = fetch_and_visualize_from_mongodb(args.request_id, args.output)
        if result:
            print(f"🎉 Open {result} in your browser to view the interactive visualization!")
    
    elif args.json_file:
        if not Path(args.json_file).exists():
            print(f"❌ File not found: {args.json_file}")
            return
        
        with open(args.json_file, 'r') as f:
            data = json.load(f)
        
        visualizer = InteractiveSynthesisVisualizer()
        result = visualizer.visualize_from_db_data(data, args.output)
        if result:
            print(f"🎉 Open {result} in your browser to view the interactive visualization!")
    
    else:
        print("❌ Please provide either --request-id or --json-file")
        parser.print_help()

if __name__ == "__main__":
    main()
