#!/bin/bash

# Set Python path from environment variable or use default
PYTHON_PATH="${RETRO_PYTHON:-/opt/ltr_runner/p310_env/bin/python}"

# Function to call the LTR script
call_ltr_script() {
    local input_path="$1"
    local output_path="$2"
    
    # Build and execute the command
    echo "Running LTR script..."
    echo "Input: $input_path"
    echo "Output: $output_path"
    echo "Python: $PYTHON_PATH"
    echo ""
    
    # Run the command and capture output
    if "$PYTHON_PATH" /opt/ltr_runner/run_ltr.py --input "$input_path" --output "$output_path"; then
        echo "<PERSON>ript completed successfully!"
    else
        exit_code=$?
        echo "<PERSON>ript failed with exit code $exit_code" >&2
        exit $exit_code
    fi
}

# Accept command line arguments
if [ $# -eq 2 ]; then
    call_ltr_script "$1" "$2"
else
    echo "Usage: $0 <input_path> <output_path>"
    echo "Example: $0 ./data/input.json ./results/output.csv"
    exit 1
fi