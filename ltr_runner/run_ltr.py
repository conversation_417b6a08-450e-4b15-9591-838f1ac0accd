import argparse
import pandas as pd
from model import get_precursor_embeddings_and_similarity, predict_ltr_scores

def main(input_path, output_path):
    # Load input file (supports CSV or JSON)
    if input_path.endswith(".csv"):
        df = pd.read_csv(input_path)
    elif input_path.endswith(".json"):
        df = pd.read_json(input_path)
    else:
        raise ValueError("Unsupported input file format. Use CSV or JSON.")

    # Check required columns
    # required_cols = [
    #     "target", "precursors",
    #     "retro_confidence_at", "retro_confidence_tr", "retro_confidence_em",
    #     "retro_confidence_auto", "retro_confidence_random", "retro_confidence_substructure_r2"
    # ]
    # missing = set(required_cols) - set(df.columns)
    # if missing:
    #     raise ValueError(f"Input data missing required columns: {missing}")

    # Run embedding + similarity
    df.rename(columns={'Target':'target', 'Retro': 'precursors'}, inplace=True)
    df = get_precursor_embeddings_and_similarity(df)

    # Run LTR prediction
    df = predict_ltr_scores(df)

    df.rename(columns={'target':'Target', 'precursors': 'Retro'}, inplace=True)
    # Save output
    df.to_csv(output_path, index=False)
    print(f"Output saved to {output_path}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run LTR prediction with ChemBERTa embeddings.")
    parser.add_argument("--input", type=str, required=True, help="Input CSV/JSON file path")
    parser.add_argument("--output", type=str, required=True, help="Output CSV file path")

    args = parser.parse_args()
    main(args.input, args.output)
