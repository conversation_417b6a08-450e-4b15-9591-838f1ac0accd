import torch
import numpy as np
import pandas as pd
from torch.utils.data import Dataset, DataLoader
from transformers import <PERSON>Tokenizer, AutoModel
from sklearn.metrics.pairwise import cosine_similarity
from tqdm import tqdm

import warnings
from rdkit import Chem
from rdkit.Chem import AllChem, DataStructs, rdChemReactions
from rdkit import RDLogger

# --- Suppress all warnings/logs ---
warnings.filterwarnings("ignore")
RDLogger.DisableLog('rdApp.*')

def canonicalize_smiles(smiles: str, remove_mapping = False) -> str:
    '''
    Molecule canonicalization that does not change the SMILES order of molecules in case of multiple molecules.
    Also neutralizes any charge of the molecules.
    
    Args:
        smiles (str): SMILES string of the molecule(s).
    
    Returns:
        str: Canonicalized SMILES string of the molecule(s).
    '''
    returned = []
    any_error = False
    for molecule in smiles.split('.'):
        molecule = neutralize_smi(molecule)
        if remove_mapping:
            molecule = remove_atom_mapping(molecule)
        mol = Chem.MolFromSmiles(molecule)
        if mol is not None:
            returned.append(Chem.MolToSmiles(mol, isomericSmiles=True, canonical=True))
        else: 
            any_error = True
    if not any_error:
        return '.'.join(returned)
    else:
        return ''

def neutralize_smi(smiles: str) -> str:        # from: https://www.rdkit.org/docs/Cookbook.html#neutralizing-molecules
    if '-' in smiles or '+' in smiles:
        try:
            mol = Chem.MolFromSmiles(smiles)
            pattern = Chem.MolFromSmarts("[+1!h0!$([*]~[-1,-2,-3,-4]),-1!$([*]~[+1,+2,+3,+4])]")
            at_matches = mol.GetSubstructMatches(pattern)
            at_matches_list = [y[0] for y in at_matches]
            if len(at_matches_list) > 0:
                for at_idx in at_matches_list:
                    atom = mol.GetAtomWithIdx(at_idx)
                    chg = atom.GetFormalCharge()
                    hcount = atom.GetTotalNumHs()
                    atom.SetFormalCharge(0)
                    atom.SetNumExplicitHs(hcount - chg)
                    atom.UpdatePropertyCache()
            return Chem.MolToSmiles(mol)
        except:
            return smiles
    else:
        return smiles

def remove_atom_mapping(smiles: str) -> str:
    try:
        mol = Chem.MolFromSmiles(smiles, sanitize=False)
        if mol is None:
            return smiles
        for atom in mol.GetAtoms():
            atom.SetAtomMapNum(0)
        return Chem.MolToSmiles(mol, isomericSmiles=True, canonical=True)
    except:
        return smiles

# === GLOBAL MODEL LOAD ===
MODEL_NAME = "DeepChem/ChemBERTa-77M-MTR"
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
model = AutoModel.from_pretrained(MODEL_NAME)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model.to(device)
model.eval()

# === Dataset and Collate ===
class SMILESDataset(Dataset):
    def __init__(self, smiles_list):
        self.smiles_list = smiles_list
    def __len__(self):
        return len(self.smiles_list)
    def __getitem__(self, idx):
        return self.smiles_list[idx]

def collate_fn(smiles_batch):
    encoded = tokenizer(
        smiles_batch,
        return_tensors="pt",
        padding=True,
        truncation=True,
        max_length=512
    )
    encoded["smiles"] = smiles_batch
    return encoded

# === Main Function ===
def get_precursor_embeddings_and_similarity(df, target_col="target", precursor_col="precursors", batch_size=64):

    df['precursors'] = df['precursors'].apply(canonicalize_smiles)
    # Normalize reactants (e.g., A.B → A.B, B.A → A.B)
    df['precursors'] = (df['precursors'].str.split('.').apply(lambda x: '.'.join(sorted(x))))

    # Step 1: Embed unique SMILES
    all_smiles = list(set(df[target_col].tolist() + df[precursor_col].tolist()))
    dataset = SMILESDataset(all_smiles)
    dataloader = DataLoader(dataset, batch_size=batch_size, collate_fn=collate_fn)

    embedding_dict = {}
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Embedding all SMILES"):
            input_ids = batch["input_ids"].to(device)
            attention_mask = batch["attention_mask"].to(device)
            smiles = batch["smiles"]

            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            hidden_states = outputs.last_hidden_state

            mask_exp = attention_mask.unsqueeze(-1).expand(hidden_states.size()).float()
            pooled = torch.sum(hidden_states * mask_exp, dim=1) / torch.clamp(mask_exp.sum(dim=1), min=1e-9)

            for smi, emb in zip(smiles, pooled.cpu().numpy()):
                embedding_dict[smi] = emb

    # Step 2: Assign precursor embeddings
    df["precursor_embedding"] = df[precursor_col].map(embedding_dict)

    # Step 3: Group by target, compute vectorized cosine similarities
    similarity_all = np.empty(len(df), dtype=np.float32)

    for target_smi, group in df.groupby(target_col):
        target_vec = embedding_dict.get(target_smi)
        if target_vec is None:
            similarity_all[group.index] = np.nan
            continue

        precursor_vecs = np.stack(group["precursor_embedding"].values)
        sims = cosine_similarity(target_vec.reshape(1, -1), precursor_vecs)[0]
        similarity_all[group.index] = sims

    df["cosine_similarity"] = similarity_all

    # Step 4: Expand embedding columns
    embedding_features = [f"embedding_{i}" for i in range(384)]
    emb_matrix = np.vstack(df["precursor_embedding"].values)
    emb_df = pd.DataFrame(emb_matrix, columns=embedding_features, index=df.index)

    # Concatenate embedding columns
    df = pd.concat([df.drop(columns=["precursor_embedding"]), emb_df], axis=1)
    return df


import xgboost as xgb

# Load once globally
ltr_model = xgb.Booster()
ltr_model.load_model("./assets/ltr/xgb_ltr_model_v1.3.bin")

numerical_features = [
    "retro_confidence_at", "retro_confidence_tr", "retro_confidence_em",
    'retro_confidence_auto', 'retro_confidence_random', 'retro_confidence_substructure_r2',
    "cosine_similarity"
]
embedding_features = [f"embedding_{i}" for i in range(384)]
all_features = numerical_features + embedding_features

def predict_ltr_scores(df: pd.DataFrame) -> pd.DataFrame:
    # Assume df includes all required numerical features
    df.rename(columns={'retro_conf_askcos_AT':'retro_confidence_at', 'retro_conf_askcos_TR': 'retro_confidence_tr',
    'retro_conf_askcos_EM': 'retro_confidence_em', 'retro_conf_AT': 'retro_confidence_auto', 
    'retro_conf_RT': 'retro_confidence_random', 'retro_conf_ST':'retro_confidence_substructure_r2'}, inplace=True)
    X = df[all_features]
    dmatrix = xgb.DMatrix(X)

    # If group is available, we can set it (optional)
    # group_sizes = df.groupby("product_id").size().tolist()
    # dmatrix.set_group(group_sizes)

    scores = ltr_model.predict(dmatrix)
    df["ltr_score"] = scores

    df.rename(columns={'retro_confidence_at': 'retro_conf_askcos_AT', 'retro_confidence_tr': 'retro_conf_askcos_TR',
    'retro_confidence_em': 'retro_conf_askcos_EM',  'retro_confidence_auto': 'retro_conf_AT', 
    'retro_confidence_random': 'retro_conf_RT' , 'retro_confidence_substructure_r2': 'retro_conf_ST'}, inplace=True)

    return df