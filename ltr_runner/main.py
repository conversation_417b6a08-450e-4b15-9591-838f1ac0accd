import subprocess
import os

def call_ltr_script(input_path, output_path):
    
    python_path = os.environ.get("RETRO_PYTHON", "/home/<USER>/retro_hf/bin/python")

    cmd = [
        python_path,
        "run_ltr.py",
        "--input", input_path,
        "--output", output_path
    ]

    result = subprocess.run(cmd, capture_output=True, text=True)
    print("STDOUT:\n", result.stdout)
    print("STDERR:\n", result.stderr)

    if result.returncode != 0:
        raise RuntimeError(f"<PERSON><PERSON><PERSON> failed with exit code {result.returncode}")

# Example usage:
call_ltr_script("./data/input.json", "./results/output.csv")

