#!/usr/bin/env python3
"""
Debug script to test and visualize the connections in synthesis tree data.
"""

import json

def analyze_route_data():
    """Analyze the route data to understand the connection structure."""
    
    sample_data = {
        "route_id": 1,
        "target_smiles": "Clc1cccnc1Cl",
        "data": [
            {
                "step": 1,
                "parent_molecule_id": "2e4d97d2-ac12-40ac-85bb-5693328f3d1a",
                "parent_reaction_id": None,
                "reaction_id": "beb42a86-28d4-4adf-8cf0-792b0450ff0e",
                "reaction_string": "ClCl.Clc1ccccn1>ClC(Cl)Cl>Clc1cccnc1Cl",
                "reactants": [
                    {"smiles": "ClCl", "is_terminal": "TERMINAL"},
                    {"smiles": "Clc1ccccn1", "is_terminal": "UNSOLVED"}
                ]
            },
            {
                "step": 2,
                "parent_molecule_id": "9e173b91-6024-4812-92cb-fd212cc8644a",
                "parent_reaction_id": "beb42a86-28d4-4adf-8cf0-792b0450ff0e",
                "reaction_id": "4ea103d8-c978-423d-a5e2-c1076f8a6eb7",
                "reaction_string": "Clc1cccc(Cl)n1>C1CCOC1.[Al+3].[H-].[H-].[H-].[H-].[Li+]>Clc1ccccn1",
                "reactants": [
                    {"smiles": "Clc1cccc(Cl)n1", "is_terminal": "TERMINAL"}
                ]
            }
        ]
    }
    
    print("🔍 ANALYZING ROUTE DATA STRUCTURE")
    print("=" * 50)
    
    target = sample_data["target_smiles"]
    print(f"🎯 Target: {target}")
    
    print("\n📊 REACTIONS:")
    for reaction in sample_data["data"]:
        step = reaction["step"]
        rxn_id = reaction["reaction_id"]
        rxn_string = reaction["reaction_string"]
        parent_mol = reaction.get("parent_molecule_id")
        parent_rxn = reaction.get("parent_reaction_id")
        
        print(f"\n  Step {step}: {rxn_id[:8]}...")
        print(f"    Reaction: {rxn_string}")
        print(f"    Parent Molecule: {parent_mol}")
        print(f"    Parent Reaction: {parent_rxn}")
        
        # Extract product from reaction string
        if '>' in rxn_string:
            parts = rxn_string.split('>')
            reactants = parts[0]
            product = parts[-1]
            print(f"    Produces: {product}")
            print(f"    From: {reactants}")
        
        print(f"    Reactants needed:")
        for i, reactant in enumerate(reaction["reactants"]):
            print(f"      {i+1}. {reactant['smiles']} ({reactant['is_terminal']})")
    
    print("\n🔗 CONNECTION ANALYSIS:")
    print("=" * 30)
    
    # Analyze what should connect to what
    reactions = sample_data["data"]
    
    # Create mapping of products to reactions
    product_to_reaction = {}
    for reaction in reactions:
        rxn_string = reaction["reaction_string"]
        if '>' in rxn_string:
            product = rxn_string.split('>')[-1].strip()
            product_to_reaction[product] = reaction["reaction_id"]
    
    print("Products produced by reactions:")
    for product, rxn_id in product_to_reaction.items():
        print(f"  {product} ← produced by {rxn_id[:8]}...")
    
    print("\nReactant dependencies:")
    for reaction in reactions:
        step = reaction["step"]
        rxn_id = reaction["reaction_id"]
        print(f"\n  Step {step} ({rxn_id[:8]}...) needs:")
        
        for reactant in reaction["reactants"]:
            reactant_smiles = reactant["smiles"]
            if reactant_smiles in product_to_reaction:
                producer = product_to_reaction[reactant_smiles]
                print(f"    ✅ {reactant_smiles} ← produced by {producer[:8]}...")
            else:
                print(f"    🔴 {reactant_smiles} ← NOT produced by any reaction (terminal)")
    
    print("\n💡 EXPECTED CONNECTIONS:")
    print("=" * 25)
    print("1. Target → Step 1 Reaction (produces target)")
    print("2. Step 1 Reaction → ClCl (terminal reactant)")
    print("3. Step 1 Reaction → Clc1ccccn1 (intermediate reactant)")
    print("4. Step 2 Reaction → Clc1ccccn1 (produces this for Step 1)")
    print("5. Step 2 Reaction → Clc1cccc(Cl)n1 (terminal reactant)")
    print("6. Step 2 → Step 1 (route dependency)")
    
    return sample_data

def create_simple_visualization():
    """Create a simple text-based visualization."""
    data = analyze_route_data()
    
    print("\n🎨 SIMPLE VISUALIZATION:")
    print("=" * 25)
    print()
    print("    Clc1cccc(Cl)n1 (TERMINAL)")
    print("           ↓")
    print("    [Step 2 Reaction]")
    print("           ↓")
    print("      Clc1ccccn1")
    print("           ↓")
    print("    [Step 1 Reaction] ← ClCl (TERMINAL)")
    print("           ↓")
    print("    Clc1cccnc1Cl (TARGET)")
    print()

if __name__ == "__main__":
    create_simple_visualization()
    
    print("\n🛠️  DEBUGGING TIPS:")
    print("1. Check if nodes are being created with correct IDs")
    print("2. Verify edge connections use the right node IDs")
    print("3. Make sure parent_reaction_id logic is correct")
    print("4. Ensure SMILES matching works properly")
    print("\n🚀 Run this to understand the data structure before fixing the visualizer!")
