<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Modern Synthesis Tree Visualizer</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-flask"></i> Synthesis Tree Visualizer</h1>
                <p>Interactive visualization of retro synthesis routes</p>
            </div>
        </header>

        <!-- Controls Panel -->
        <div class="controls-panel">
            <div class="control-group">
                <label for="requestId">Request ID:</label>
                <input type="text" id="requestId" placeholder="Enter request ID (e.g., os6df8a82-9fae-479e-ba10-e92e0f8f9492)">
                <button id="loadBtn" class="btn btn-primary">
                    <i class="fas fa-search"></i> Load Routes
                </button>
                <button id="sampleBtn" class="btn btn-secondary">
                    <i class="fas fa-vial"></i> Load Sample
                </button>
            </div>

            <div class="control-group">
                <button id="resetBtn" class="btn btn-outline">
                    <i class="fas fa-redo"></i> Reset View
                </button>
                <button id="exportBtn" class="btn btn-outline">
                    <i class="fas fa-download"></i> Export PNG
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Info Panel -->
            <div class="info-panel" id="infoPanel">
                <div class="info-header">
                    <h3><i class="fas fa-info-circle"></i> Route Information</h3>
                    <button id="closeInfo" class="close-btn">&times;</button>
                </div>
                <div class="info-content" id="infoContent">
                    <p>Click on any node to see detailed information</p>
                </div>
            </div>

            <!-- Visualization Container -->
            <div class="viz-container">
                <div id="loading" class="loading">
                    <div class="spinner"></div>
                    <p>Loading synthesis routes...</p>
                </div>
                <div id="error" class="error" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p id="errorMessage">Error loading data</p>
                </div>
                <svg id="visualization"></svg>
            </div>
        </div>

        <!-- Legend -->
        <div class="legend">
            <h4><i class="fas fa-palette"></i> Legend</h4>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="legend-color target"></div>
                    <span>Target Molecule</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color reaction"></div>
                    <span>Reaction Step</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color terminal"></div>
                    <span>Terminal/Building Block</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color intermediate"></div>
                    <span>Intermediate Molecule</span>
                </div>
            </div>
            <div class="legend-edges">
                <div class="legend-item">
                    <div class="legend-line synthesis"></div>
                    <span>Synthesis Flow</span>
                </div>
                <div class="legend-item">
                    <div class="legend-line dependency"></div>
                    <span>Route Dependency</span>
                </div>
                <div class="legend-item">
                    <div class="legend-line produces"></div>
                    <span>Produces</span>
                </div>
            </div>
        </div>
    </div>

    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="{{ url_for('static', filename='js/visualizer.js') }}"></script>
</body>
</html>