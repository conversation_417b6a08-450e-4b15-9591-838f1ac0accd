#!/usr/bin/env python3
"""
Debug script to test MongoDB connection and API functionality
"""

import sys
from modern_visualizer import ModernSynthesisVisualizer

def test_mongodb_connection():
    """Test MongoDB connection."""
    print("🔍 TESTING MONGODB CONNECTION")
    print("=" * 40)
    
    visualizer = ModernSynthesisVisualizer()
    
    try:
        success = visualizer.connect_to_mongodb()
        if success:
            print("✅ MongoDB connection successful")
            
            # Test collection access
            if visualizer.collection is not None:
                print("✅ Collection access successful")
                
                # Test a simple query
                count = visualizer.collection.count_documents({})
                print(f"📊 Total documents in collection: {count}")
                
                # Test finding a specific request
                test_request_id = "os6df8a82-9fae-479e-ba10-e92e0f8f9492"
                routes = visualizer.get_route_data(test_request_id)
                print(f"📊 Routes found for {test_request_id}: {len(routes)}")
                
                if routes:
                    route = routes[0]
                    print(f"✅ Sample route data:")
                    print(f"   Target: {route.get('target_smiles', 'N/A')}")
                    print(f"   Steps: {route.get('num_steps', 'N/A')}")
                    print(f"   Route ID: {route.get('route_id', 'N/A')}")
                    
                    # Test data transformation
                    graph_data = visualizer.transform_data_for_d3(routes)
                    print(f"✅ Graph transformation successful:")
                    print(f"   Nodes: {len(graph_data['nodes'])}")
                    print(f"   Links: {len(graph_data['links'])}")
                    
                    # Show node types
                    node_types = {}
                    for node in graph_data['nodes']:
                        node_type = node.get('type', 'unknown')
                        node_types[node_type] = node_types.get(node_type, 0) + 1
                    
                    print(f"   Node types: {node_types}")
                    
                    return True
                else:
                    print(f"❌ No routes found for test request ID")
                    
                    # Try to find any routes
                    sample_routes = list(visualizer.collection.find().limit(1))
                    if sample_routes:
                        sample_route = sample_routes[0]
                        sample_request_id = sample_route.get('request_id', 'N/A')
                        print(f"💡 Try using this request ID instead: {sample_request_id}")
                    else:
                        print("❌ No routes found in the database at all")
                    
                    return False
            else:
                print("❌ Collection is None")
                return False
        else:
            print("❌ MongoDB connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during MongoDB test: {e}")
        return False

def test_sample_data():
    """Test sample data transformation."""
    print("\n🧪 TESTING SAMPLE DATA TRANSFORMATION")
    print("=" * 40)
    
    visualizer = ModernSynthesisVisualizer()
    
    sample_routes = [{
        "route_id": 1,
        "target_smiles": "Clc1cccnc1Cl",
        "total_route_score": 0.502830946,
        "num_steps": 2,
        "data": [
            {
                "step": 1,
                "parent_reaction_id": None,
                "reaction_id": "beb42a86-28d4-4adf-8cf0-792b0450ff0e",
                "reaction_string": "ClCl.Clc1ccccn1>ClC(Cl)Cl>Clc1cccnc1Cl",
                "score": 0.497,
                "rxn_class": {"reaction_name": "Chlorination"},
                "reactants": [
                    {"smiles": "ClCl", "name": "Chlorine", "is_terminal": "TERMINAL", "synthesis_score": 1.225},
                    {"smiles": "Clc1ccccn1", "name": "2-chloropyridine", "is_terminal": "UNSOLVED", "synthesis_score": 1.238}
                ]
            },
            {
                "step": 2,
                "parent_reaction_id": "beb42a86-28d4-4adf-8cf0-792b0450ff0e",
                "reaction_id": "4ea103d8-c978-423d-a5e2-c1076f8a6eb7",
                "reaction_string": "Clc1cccc(Cl)n1>C1CCOC1.[Al+3].[H-].[H-].[H-].[H-].[Li+]>Clc1ccccn1",
                "score": 0.508,
                "rxn_class": {"reaction_name": "Dechlorination"},
                "reactants": [
                    {"smiles": "Clc1cccc(Cl)n1", "name": "2,6-dichloropyridine", "is_terminal": "TERMINAL", "synthesis_score": 1.228}
                ]
            }
        ]
    }]
    
    try:
        graph_data = visualizer.transform_data_for_d3(sample_routes)
        print(f"✅ Sample data transformation successful:")
        print(f"   Nodes: {len(graph_data['nodes'])}")
        print(f"   Links: {len(graph_data['links'])}")
        
        print(f"\n📊 NODES:")
        for node in graph_data['nodes']:
            print(f"   {node['id']} ({node['type']}): {node['label']}")
        
        print(f"\n🔗 LINKS:")
        for link in graph_data['links']:
            print(f"   {link['source']} --{link['type']}--> {link['target']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during sample data test: {e}")
        return False

def main():
    """Main debug function."""
    print("🐛 API DEBUG SCRIPT")
    print("=" * 50)
    
    # Test sample data first (doesn't require MongoDB)
    sample_success = test_sample_data()
    
    # Test MongoDB connection
    mongodb_success = test_mongodb_connection()
    
    print(f"\n📋 SUMMARY:")
    print(f"   Sample data transformation: {'✅ PASS' if sample_success else '❌ FAIL'}")
    print(f"   MongoDB connection: {'✅ PASS' if mongodb_success else '❌ FAIL'}")
    
    if sample_success and not mongodb_success:
        print(f"\n💡 RECOMMENDATION:")
        print(f"   The sample data works fine. Try using 'Load Sample' button in the UI.")
        print(f"   For MongoDB data, check your connection string and database contents.")
    
    elif sample_success and mongodb_success:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"   The visualizer should work perfectly. Run:")
        print(f"   python run_modern_visualizer.py")
    
    else:
        print(f"\n❌ ISSUES DETECTED:")
        print(f"   Check the error messages above and fix the issues.")

if __name__ == "__main__":
    main()
