// Modern Synthesis Tree Visualizer - D3.js Implementation
class SynthesisVisualizer {
    constructor() {
        this.svg = null;
        this.simulation = null;
        this.nodes = [];
        this.links = [];
        this.width = 0;
        this.height = 0;
        this.tooltip = null;
        this.selectedNode = null;

        this.colors = {
            target: '#ff6b6b',
            reaction: '#4ecdc4',
            terminal: '#45b7d1',
            intermediate: '#96ceb4'
        };

        this.linkColors = {
            synthesis: '#ff6b6b',
            dependency: '#feca57',
            requires: '#74b9ff',
            produces: '#48dbfb'
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.createTooltip();
        this.setupSVG();
    }

    setupEventListeners() {
        document.getElementById('loadBtn').addEventListener('click', () => {
            const requestId = document.getElementById('requestId').value.trim();
            if (requestId) {
                this.loadRoutes(requestId);
            } else {
                this.showError('Please enter a request ID');
            }
        });

        document.getElementById('sampleBtn').addEventListener('click', () => {
            this.loadSampleData();
        });

        document.getElementById('resetBtn').addEventListener('click', () => {
            this.resetView();
        });

        document.getElementById('exportBtn').addEventListener('click', () => {
            this.exportPNG();
        });

        document.getElementById('closeInfo').addEventListener('click', () => {
            this.hideInfoPanel();
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    createTooltip() {
        this.tooltip = d3.select('body')
            .append('div')
            .attr('class', 'tooltip')
            .style('opacity', 0);
    }

    setupSVG() {
        const container = document.getElementById('visualization');
        this.width = container.clientWidth;
        this.height = container.clientHeight;

        this.svg = d3.select('#visualization')
            .attr('width', this.width)
            .attr('height', this.height);

        // Add zoom behavior
        const zoom = d3.zoom()
            .scaleExtent([0.1, 4])
            .on('zoom', (event) => {
                this.svg.select('.main-group')
                    .attr('transform', event.transform);
            });

        this.svg.call(zoom);

        // Create main group for zooming/panning
        this.svg.append('g').attr('class', 'main-group');
    }

    async loadRoutes(requestId) {
        this.showLoading();
        try {
            const response = await fetch(`/api/routes/${requestId}`);
            const data = await response.json();

            if (data.success) {
                this.renderVisualization(data.data, data.metadata);
                this.hideLoading();
            } else {
                this.showError(data.error || 'Failed to load routes');
            }
        } catch (error) {
            this.showError('Network error: ' + error.message);
        }
    }

    async loadSampleData() {
        this.showLoading();
        try {
            const response = await fetch('/api/sample');
            const data = await response.json();

            if (data.success) {
                this.renderVisualization(data.data, data.metadata);
                this.hideLoading();
            } else {
                this.showError('Failed to load sample data');
            }
        } catch (error) {
            this.showError('Network error: ' + error.message);
        }
    }

    renderVisualization(graphData, metadata) {
        this.nodes = graphData.nodes;
        this.links = graphData.links;

        // Clear previous visualization
        this.svg.select('.main-group').selectAll('*').remove();

        // Create force simulation
        this.simulation = d3.forceSimulation(this.nodes)
            .force('link', d3.forceLink(this.links).id(d => d.id).distance(100))
            .force('charge', d3.forceManyBody().strength(-300))
            .force('center', d3.forceCenter(this.width / 2, this.height / 2))
            .force('collision', d3.forceCollide().radius(d => d.size + 5));

        const mainGroup = this.svg.select('.main-group');

        // Create links
        const link = mainGroup.append('g')
            .attr('class', 'links')
            .selectAll('line')
            .data(this.links)
            .enter().append('line')
            .attr('class', 'link')
            .attr('stroke', d => this.linkColors[d.type] || '#999')
            .attr('stroke-width', 2)
            .attr('stroke-dasharray', d => d.type === 'dependency' ? '5,5' : null);

        // Create nodes
        const node = mainGroup.append('g')
            .attr('class', 'nodes')
            .selectAll('circle')
            .data(this.nodes)
            .enter().append('circle')
            .attr('class', 'node')
            .attr('r', d => d.size)
            .attr('fill', d => this.colors[d.type] || '#999')
            .attr('stroke', '#fff')
            .attr('stroke-width', 2)
            .call(this.drag());

        // Add labels
        const labels = mainGroup.append('g')
            .attr('class', 'labels')
            .selectAll('text')
            .data(this.nodes)
            .enter().append('text')
            .attr('class', 'node-label')
            .text(d => this.truncateLabel(d.label))
            .attr('dy', '.35em');

        // Add event listeners
        node
            .on('mouseover', (event, d) => this.showTooltip(event, d))
            .on('mouseout', () => this.hideTooltip())
            .on('click', (event, d) => this.selectNode(d));

        // Update positions on simulation tick
        this.simulation.on('tick', () => {
            link
                .attr('x1', d => d.source.x)
                .attr('y1', d => d.source.y)
                .attr('x2', d => d.target.x)
                .attr('y2', d => d.target.y);

            node
                .attr('cx', d => d.x)
                .attr('cy', d => d.y);

            labels
                .attr('x', d => d.x)
                .attr('y', d => d.y);
        });

        // Show metadata
        this.showMetadata(metadata);
    }

    drag() {
        return d3.drag()
            .on('start', (event, d) => {
                if (!event.active) this.simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            })
            .on('drag', (event, d) => {
                d.fx = event.x;
                d.fy = event.y;
            })
            .on('end', (event, d) => {
                if (!event.active) this.simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            });
    }

    showTooltip(event, d) {
        const details = d.details || {};
        let content = `<strong>${details.type || 'Node'}</strong><br>`;

        Object.entries(details).forEach(([key, value]) => {
            if (key !== 'type' && value !== 'N/A') {
                content += `<strong>${key}:</strong> ${value}<br>`;
            }
        });

        this.tooltip.transition()
            .duration(200)
            .style('opacity', .9);
        this.tooltip.html(content)
            .style('left', (event.pageX + 10) + 'px')
            .style('top', (event.pageY - 28) + 'px');
    }

    hideTooltip() {
        this.tooltip.transition()
            .duration(500)
            .style('opacity', 0);
    }

    selectNode(d) {
        // Remove previous selection
        this.svg.selectAll('.node').classed('selected', false);

        // Select current node
        this.svg.selectAll('.node')
            .filter(node => node.id === d.id)
            .classed('selected', true);

        this.selectedNode = d;
        this.showInfoPanel(d);
    }

    showInfoPanel(d) {
        const panel = document.getElementById('infoPanel');
        const content = document.getElementById('infoContent');

        let html = `<h4>${d.label}</h4>`;
        html += `<div class="info-details">`;

        Object.entries(d.details || {}).forEach(([key, value]) => {
            if (value !== 'N/A') {
                html += `<div class="info-item">
                    <strong>${key}:</strong> <span>${value}</span>
                </div>`;
            }
        });

        html += `</div>`;
        content.innerHTML = html;
        panel.classList.add('active');
    }

    hideInfoPanel() {
        document.getElementById('infoPanel').classList.remove('active');
        this.svg.selectAll('.node').classed('selected', false);
        this.selectedNode = null;
    }

    showMetadata(metadata) {
        console.log('Route metadata:', metadata);
        // You can display metadata in the UI if needed
    }

    truncateLabel(label, maxLength = 20) {
        return label.length > maxLength ? label.substring(0, maxLength) + '...' : label;
    }

    resetView() {
        if (this.svg) {
            this.svg.transition()
                .duration(750)
                .call(
                    d3.zoom().transform,
                    d3.zoomIdentity
                );
        }
    }

    exportPNG() {
        // Implementation for PNG export
        const svgElement = document.getElementById('visualization');
        const serializer = new XMLSerializer();
        const svgString = serializer.serializeToString(svgElement);

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();

        img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);

            const link = document.createElement('a');
            link.download = 'synthesis-tree.png';
            link.href = canvas.toDataURL();
            link.click();
        };

        img.src = 'data:image/svg+xml;base64,' + btoa(svgString);
    }

    showLoading() {
        document.getElementById('loading').style.display = 'block';
        document.getElementById('error').style.display = 'none';
    }

    hideLoading() {
        document.getElementById('loading').style.display = 'none';
    }

    showError(message) {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('error').style.display = 'block';
        document.getElementById('errorMessage').textContent = message;
    }

    handleResize() {
        const container = document.getElementById('visualization');
        this.width = container.clientWidth;
        this.height = container.clientHeight;

        this.svg
            .attr('width', this.width)
            .attr('height', this.height);

        if (this.simulation) {
            this.simulation
                .force('center', d3.forceCenter(this.width / 2, this.height / 2))
                .restart();
        }
    }
}

// Initialize the visualizer when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new SynthesisVisualizer();
});