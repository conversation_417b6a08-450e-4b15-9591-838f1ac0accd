/* Modern Synthesis Tree Visualizer Styles */
:root {
    --primary-color: #2563eb;
    --secondary-color: #7c3aed;
    --success-color: #059669;
    --warning-color: #d97706;
    --error-color: #dc2626;
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-dark: #1e293b;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--text-primary);
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    box-shadow: var(--shadow);
}

.header-content h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.header-content p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* Controls Panel */
.controls-panel {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.control-group label {
    font-weight: 500;
    color: var(--text-primary);
}

.control-group input {
    padding: 0.5rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.9rem;
    min-width: 300px;
    transition: all 0.2s ease;
}

.control-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Button Styles */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #6d28d9;
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-outline {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-outline:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    position: relative;
    overflow: hidden;
}

/* Info Panel */
.info-panel {
    width: 350px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-right: 1px solid var(--border-color);
    padding: 1.5rem;
    overflow-y: auto;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.info-panel.active {
    transform: translateX(0);
}

.info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.info-header h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0.25rem;
}

.close-btn:hover {
    color: var(--error-color);
}

/* Visualization Container */
.viz-container {
    flex: 1;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
}

#visualization {
    width: 100%;
    height: 100%;
}

/* Loading and Error States */
.loading, .error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    color: var(--error-color);
    background: rgba(255, 255, 255, 0.9);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
}

/* Legend */
.legend {
    position: absolute;
    bottom: 2rem;
    right: 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    min-width: 200px;
}

.legend h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.legend-items, .legend-edges {
    margin-bottom: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
}

.legend-color.target { background: #ff6b6b; }
.legend-color.reaction { background: #4ecdc4; }
.legend-color.terminal { background: #45b7d1; }
.legend-color.intermediate { background: #96ceb4; }

.legend-line {
    width: 20px;
    height: 3px;
    border-radius: 2px;
}

.legend-line.synthesis { background: #ff6b6b; }
.legend-line.dependency { background: #feca57; }
.legend-line.produces { background: #48dbfb; }

/* Responsive Design */
@media (max-width: 768px) {
    .controls-panel {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group input {
        min-width: auto;
    }

    .info-panel {
        width: 100%;
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        z-index: 1000;
    }

    .legend {
        position: relative;
        bottom: auto;
        right: auto;
        margin: 1rem;
    }
}

/* Node and Link Styles for D3 */
.node {
    cursor: pointer;
    transition: all 0.2s ease;
}

.node:hover {
    stroke-width: 3px;
}

.node.selected {
    stroke: var(--primary-color);
    stroke-width: 4px;
}

.link {
    fill: none;
    stroke-width: 2px;
    transition: all 0.2s ease;
}

.link:hover {
    stroke-width: 4px;
}

.node-label {
    font-family: 'Inter', sans-serif;
    font-size: 12px;
    font-weight: 500;
    text-anchor: middle;
    pointer-events: none;
    fill: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.75rem;
    border-radius: 8px;
    font-size: 0.85rem;
    pointer-events: none;
    z-index: 1000;
    max-width: 300px;
    box-shadow: var(--shadow-lg);
}