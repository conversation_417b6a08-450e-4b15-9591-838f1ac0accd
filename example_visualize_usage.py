#!/usr/bin/env python3
"""
Example usage of the synthesis tree visualizer.

This script demonstrates how to use the visualizer with different data sources.
"""

import json
from visualize_synthesis_tree import SynthesisTreeVisualizer

def example_with_sample_data():
    """Example using sample database data."""
    
    # Sample data similar to what you provided
    sample_db_data = {
        "_id": "bb564c09-e620-48b4-b462-6df77c286c8b",
        "route_id": 1,
        "target_smiles": "Clc1cccnc1Cl",
        "num_steps": 2,
        "data": [
            {
                "step": 1,
                "parent_molecule_id": "2e4d97d2-ac12-40ac-85bb-5693328f3d1a",
                "parent_reaction_id": None,
                "reaction_id": "beb42a86-28d4-4adf-8cf0-792b0450ff0e",
                "reaction_string": "ClCl.Clc1ccccn1>ClC(Cl)Cl>Clc1cccnc1Cl",
                "retro_smiles": "ClCl.Clc1ccccn1",
                "reactants": [
                    {
                        "smiles": "ClCl",
                        "name": "",
                        "synthesis_score": 1.2253340421847752,
                        "is_terminal": "TERMINAL"
                    },
                    {
                        "smiles": "Clc1ccccn1",
                        "name": "",
                        "synthesis_score": 1.2376308399757947,
                        "is_terminal": "UNSOLVED"
                    }
                ]
            },
            {
                "step": 2,
                "parent_molecule_id": "9e173b91-6024-4812-92cb-fd212cc8644a",
                "parent_reaction_id": "beb42a86-28d4-4adf-8cf0-792b0450ff0e",
                "reaction_id": "4ea103d8-c978-423d-a5e2-c1076f8a6eb7",
                "reaction_string": "Clc1cccc(Cl)n1>C1CCOC1.[Al+3].[H-].[H-].[H-].[H-].[Li+]>Clc1ccccn1",
                "retro_smiles": "Clc1cccc(Cl)n1",
                "reactants": [
                    {
                        "smiles": "Clc1cccc(Cl)n1",
                        "name": "",
                        "synthesis_score": 1.2276062370620435,
                        "is_terminal": "TERMINAL"
                    }
                ]
            }
        ]
    }
    
    # Save sample data to file
    with open('sample_route_data.json', 'w') as f:
        json.dump(sample_db_data, f, indent=2)
    
    # Create visualizer and generate graph
    visualizer = SynthesisTreeVisualizer()
    result = visualizer.visualize_from_db_data(sample_db_data, "sample_synthesis_tree")
    
    if result:
        print(f"✅ Sample visualization created: {result}")
        print("\n📋 Legend:")
        print("  🔵 Blue solid arrows: Tree parent relationships")
        print("  🔴 Red dashed arrows: Route dependency relationships")
        print("  ⚫ Gray arrows: Reaction produces reactant")
        print("\n🎨 Node colors:")
        print("  🟦 Light blue: Molecule nodes")
        print("  🟨 Light yellow: Reaction nodes")
        print("  🟩 Light green: Terminal molecules")
        print("  🟧 Light orange: Target molecule")
    
    return result

def create_visualization_from_mongodb():
    """
    Example function to create visualization from MongoDB data.
    You can adapt this to your database connection.
    """
    # This is a template - replace with your actual MongoDB connection
    """
    from pymongo import MongoClient
    
    # Connect to your MongoDB
    client = MongoClient('your_connection_string')
    db = client['your_database']
    collection = db['your_collection']
    
    # Fetch a specific route
    route_data = collection.find_one({"route_id": 1, "target_smiles": "Clc1cccnc1Cl"})
    
    if route_data:
        visualizer = SynthesisTreeVisualizer()
        result = visualizer.visualize_from_db_data(route_data, f"route_{route_data['route_id']}")
        print(f"Visualization saved: {result}")
    """
    pass

def batch_visualize_routes():
    """
    Example function to create visualizations for multiple routes.
    """
    # This is a template for batch processing

    from pymongo import MongoClient
    
    client = MongoClient('**************************************')
    db = client['retro_synthesis']
    collection = db['retro_data']
    
    # Find all routes for a specific target
    routes = collection.find({"request_id": "os6df8a82-9fae-479e-ba10-e92e0f8f9492"})
    
    visualizer = SynthesisTreeVisualizer()
    
    for route in routes:
        route_id = route.get('route_id', 'unknown')
        output_name = f"route_{route_id}_{route['target_smiles'].replace('/', '_')}"
        
        result = visualizer.visualize_from_db_data(route, output_name)
        if result:
            print(f"Created visualization: {result}")

    pass

if __name__ == "__main__":
    print("🎨 Synthesis Tree Visualizer Example")
    print("=" * 40)
    
    # Run the example
    batch_visualize_routes()
    
    print("\n📝 Usage Instructions:")
    print("1. Install graphviz: pip install graphviz")
    print("2. Install graphviz system package (varies by OS):")
    print("   - Ubuntu/Debian: sudo apt-get install graphviz")
    print("   - macOS: brew install graphviz")
    print("   - Windows: Download from https://graphviz.org/download/")
    print("\n3. Run the visualizer:")
    print("   python visualize_synthesis_tree.py --db-data sample_route_data.json")
    print("   python visualize_synthesis_tree.py --tree-object tree.pkl")
    
    print("\n🔍 The visualization will help you verify:")
    print("   - Parent-child relationships are correct")
    print("   - Route dependencies are properly mapped")
    print("   - Tree structure matches expected synthesis pathway")
