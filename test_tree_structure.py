#!/usr/bin/env python3
"""
Test the tree structure to ensure single connected tree
"""

def test_tree_structure():
    """Test that we create a single connected tree structure."""
    
    # Sample data
    sample_routes = [{
        "route_id": 1,
        "target_smiles": "Clc1cccnc1Cl",
        "total_route_score": 0.502830946,
        "num_steps": 2,
        "data": [
            {
                "step": 1,
                "parent_reaction_id": None,
                "reaction_id": "beb42a86-28d4-4adf-8cf0-792b0450ff0e",
                "reaction_string": "ClCl.Clc1ccccn1>ClC(Cl)Cl>Clc1cccnc1Cl",
                "score": 0.497,
                "rxn_class": {"reaction_name": "Chlorination"},
                "reactants": [
                    {"smiles": "ClCl", "name": "Chlorine", "is_terminal": "TERMINAL"},
                    {"smiles": "Clc1ccccn1", "name": "2-chloropyridine", "is_terminal": "UNSOLVED"}
                ]
            },
            {
                "step": 2,
                "parent_reaction_id": "beb42a86-28d4-4adf-8cf0-792b0450ff0e",
                "reaction_id": "4ea103d8-c978-423d-a5e2-c1076f8a6eb7",
                "reaction_string": "Clc1cccc(Cl)n1>C1CCOC1.[Al+3].[H-].[H-].[H-].[H-].[Li+]>Clc1ccccn1",
                "score": 0.508,
                "rxn_class": {"reaction_name": "Dechlorination"},
                "reactants": [
                    {"smiles": "Clc1cccc(Cl)n1", "name": "2,6-dichloropyridine", "is_terminal": "TERMINAL"}
                ]
            }
        ]
    }]
    
    # Simulate the transform_data_for_d3 logic
    route = sample_routes[0]
    target_smiles = route.get('target_smiles', 'Unknown')
    
    nodes = []
    links = []
    node_id_map = {}
    
    print("🧪 TESTING TREE STRUCTURE")
    print("=" * 40)
    
    # Add target node
    target_node_id = "target"
    nodes.append({
        "id": target_node_id,
        "label": f"TARGET\n{target_smiles}",
        "type": "target"
    })
    node_id_map[target_node_id] = 0
    print(f"✅ Added target: {target_smiles}")
    
    # Process reactions
    reactions = route.get('data', [])
    reactions.sort(key=lambda x: x.get('step', 0))
    
    # Create SMILES to producer mapping
    smiles_to_producer = {}
    for reaction in reactions:
        reaction_string = reaction.get('reaction_string', '')
        if '>' in reaction_string:
            product = reaction_string.split('>')[-1].strip()
            smiles_to_producer[product] = reaction.get('reaction_id')
    
    smiles_to_producer[target_smiles] = "target"
    
    print(f"\n📊 SMILES TO PRODUCER MAPPING:")
    for smiles, producer in smiles_to_producer.items():
        print(f"  {smiles} ← {producer[:8]}...")
    
    # Add reaction nodes
    for reaction in reactions:
        step = reaction.get('step', 0)
        reaction_id = reaction.get('reaction_id', 'unknown')
        parent_rxn_id = reaction.get('parent_reaction_id')
        
        reaction_node_id = f"rxn_{reaction_id}"
        nodes.append({
            "id": reaction_node_id,
            "label": f"Step {step}",
            "type": "reaction"
        })
        node_id_map[reaction_node_id] = len(nodes) - 1
        print(f"✅ Added reaction: Step {step}")
        
        # Connect target to first step
        if step == 1:
            links.append({
                "source": target_node_id,
                "target": reaction_node_id,
                "type": "synthesis",
                "label": "produces"
            })
            print(f"  └─ Connected target → Step {step}")
        
        # Add route dependency
        if parent_rxn_id:
            parent_node_id = f"rxn_{parent_rxn_id}"
            links.append({
                "source": parent_node_id,
                "target": reaction_node_id,
                "type": "dependency",
                "label": "enables"
            })
            print(f"  └─ Added dependency: {parent_rxn_id[:8]}... → Step {step}")
        
        # Add reactant nodes
        reactants = reaction.get('reactants', [])
        for reactant in reactants:
            reactant_smiles = reactant.get('smiles', 'Unknown')
            is_terminal = reactant.get('is_terminal', 'UNKNOWN')
            
            reactant_node_id = f"mol_{reactant_smiles.replace('.', '_').replace('(', '').replace(')', '')}"
            
            # Only add if doesn't exist
            if reactant_node_id not in node_id_map:
                nodes.append({
                    "id": reactant_node_id,
                    "label": reactant_smiles,
                    "type": "terminal" if is_terminal == "TERMINAL" else "intermediate"
                })
                node_id_map[reactant_node_id] = len(nodes) - 1
                print(f"✅ Added reactant: {reactant_smiles} ({is_terminal})")
            
            # Connect reaction to reactant
            links.append({
                "source": reaction_node_id,
                "target": reactant_node_id,
                "type": "requires",
                "label": "needs"
            })
            print(f"  └─ Step {step} needs {reactant_smiles}")
            
            # Connect producer to reactant
            if reactant_smiles in smiles_to_producer:
                producer_id = smiles_to_producer[reactant_smiles]
                if producer_id != "target":
                    producer_node_id = f"rxn_{producer_id}"
                    links.append({
                        "source": producer_node_id,
                        "target": reactant_node_id,
                        "type": "produces",
                        "label": "synthesizes"
                    })
                    print(f"  └─ {producer_id[:8]}... produces {reactant_smiles}")
    
    print(f"\n📈 FINAL STRUCTURE:")
    print(f"  Nodes: {len(nodes)}")
    print(f"  Links: {len(links)}")
    
    print(f"\n🔗 ALL CONNECTIONS:")
    for link in links:
        source_short = link["source"][:12] + "..." if len(link["source"]) > 12 else link["source"]
        target_short = link["target"][:12] + "..." if len(link["target"]) > 12 else link["target"]
        print(f"  {source_short} --{link['type']}--> {target_short}")
    
    # Verify connectivity
    print(f"\n✅ CONNECTIVITY CHECK:")
    
    # Check if all nodes are reachable from target
    visited = set()
    
    def dfs(node_id):
        if node_id in visited:
            return
        visited.add(node_id)
        for link in links:
            if link["source"] == node_id:
                dfs(link["target"])
    
    dfs("target")
    
    all_connected = len(visited) == len(nodes)
    print(f"  All nodes reachable from target: {'✅ YES' if all_connected else '❌ NO'}")
    print(f"  Visited: {len(visited)}/{len(nodes)} nodes")
    
    if not all_connected:
        unreachable = [node["id"] for node in nodes if node["id"] not in visited]
        print(f"  Unreachable nodes: {unreachable}")
    
    return all_connected

if __name__ == "__main__":
    success = test_tree_structure()
    
    if success:
        print(f"\n🎉 TREE STRUCTURE TEST PASSED!")
        print(f"The visualization should now show a single connected tree starting from the target.")
    else:
        print(f"\n❌ TREE STRUCTURE TEST FAILED!")
        print(f"There are disconnected nodes that need to be fixed.")
    
    print(f"\n🚀 Run the modern visualizer to see the improved structure!")
    print(f"   python run_modern_visualizer.py")
