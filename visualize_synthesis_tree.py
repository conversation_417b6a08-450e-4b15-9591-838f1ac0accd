#!/usr/bin/env python3
"""
Synthesis Tree Visualizer

This script creates visual representations of synthesis trees to help verify
parent-child relationships and route structures in retro synthesis data.

Usage:
    python visualize_synthesis_tree.py --help
    python visualize_synthesis_tree.py --db-data route_data.json
    python visualize_synthesis_tree.py --tree-object tree.pkl
"""

import argparse
import json
import pickle
import sys
from typing import Dict, List, Any, Optional
import graphviz
from pathlib import Path

class SynthesisTreeVisualizer:
    """Visualizes synthesis trees using graphviz."""
    
    def __init__(self):
        self.graph = None
        self.node_colors = {
            'molecule': '#E8F4FD',  # Light blue
            'reaction': '#FFF2CC',  # Light yellow
            'terminal': '#D5E8D4',  # Light green
            'target': '#FFE6CC'     # Light orange
        }
        self.edge_colors = {
            'tree_parent': 'blue',
            'route_parent': 'red',
            'reactant': 'gray'
        }
    
    def create_graph(self, title: str = "Synthesis Tree") -> graphviz.Digraph:
        """Create a new graphviz digraph."""
        self.graph = graphviz.Digraph(
            name='synthesis_tree',
            comment=title,
            format='png'
        )
        self.graph.attr(rankdir='TB', size='12,8')
        self.graph.attr('node', shape='box', style='filled')
        return self.graph
    
    def visualize_from_db_data(self, db_data: Dict[str, Any], output_path: str = "synthesis_tree_db"):
        """
        Visualize synthesis tree from database route data.
        
        Args:
            db_data: Database document containing route data
            output_path: Output file path (without extension)
        """
        self.create_graph(f"Route {db_data.get('route_id', 'Unknown')} - {db_data.get('target_smiles', 'Unknown')}")
        
        # Add target molecule node
        target_smiles = db_data.get('target_smiles', 'Unknown')
        self.graph.node(
            'target',
            label=f"TARGET\\n{target_smiles}",
            fillcolor=self.node_colors['target'],
            shape='ellipse'
        )
        
        # Process each reaction step
        reactions = db_data.get('data', [])
        molecule_nodes = set()
        
        for reaction in reactions:
            step = reaction.get('step', 0)
            reaction_id = reaction.get('reaction_id', 'unknown')
            parent_mol_id = reaction.get('parent_molecule_id') or reaction.get('parent')
            parent_rxn_id = reaction.get('parent_reaction_id')
            reaction_string = reaction.get('reaction_string', 'N/A')
            
            # Add reaction node
            reaction_label = f"STEP {step}\\nReaction: {reaction_id[:8]}...\\n{reaction_string}"
            self.graph.node(
                reaction_id,
                label=reaction_label,
                fillcolor=self.node_colors['reaction'],
                shape='box'
            )
            
            # Add edge from parent molecule to reaction (tree structure)
            if parent_mol_id:
                if parent_mol_id not in molecule_nodes:
                    # Create parent molecule node if it doesn't exist
                    self.graph.node(
                        parent_mol_id,
                        label=f"Molecule\\n{parent_mol_id[:8]}...",
                        fillcolor=self.node_colors['molecule'],
                        shape='ellipse'
                    )
                    molecule_nodes.add(parent_mol_id)
                
                self.graph.edge(
                    parent_mol_id,
                    reaction_id,
                    color=self.edge_colors['tree_parent'],
                    label='tree parent'
                )
            else:
                # Connect to target if no parent
                self.graph.edge(
                    'target',
                    reaction_id,
                    color=self.edge_colors['tree_parent'],
                    label='from target'
                )
            
            # Add edge from parent reaction (route dependency)
            if parent_rxn_id:
                self.graph.edge(
                    parent_rxn_id,
                    reaction_id,
                    color=self.edge_colors['route_parent'],
                    label='route dependency',
                    style='dashed'
                )
            
            # Add reactant nodes
            reactants = reaction.get('reactants', [])
            for i, reactant in enumerate(reactants):
                reactant_smiles = reactant.get('smiles', 'Unknown')
                is_terminal = reactant.get('is_terminal', 'UNKNOWN')
                
                reactant_id = f"{reaction_id}_reactant_{i}"
                color = self.node_colors['terminal'] if is_terminal == 'TERMINAL' else self.node_colors['molecule']
                
                self.graph.node(
                    reactant_id,
                    label=f"{'TERMINAL' if is_terminal == 'TERMINAL' else 'MOLECULE'}\\n{reactant_smiles}",
                    fillcolor=color,
                    shape='ellipse'
                )
                
                # Edge from reaction to reactant
                self.graph.edge(
                    reaction_id,
                    reactant_id,
                    color=self.edge_colors['reactant'],
                    label='produces'
                )
        
        # Add legend
        with self.graph.subgraph(name='cluster_legend') as legend:
            legend.attr(label='Legend', style='filled', fillcolor='lightgray')
            legend.node('leg_mol', 'Molecule', fillcolor=self.node_colors['molecule'], shape='ellipse')
            legend.node('leg_rxn', 'Reaction', fillcolor=self.node_colors['reaction'], shape='box')
            legend.node('leg_term', 'Terminal', fillcolor=self.node_colors['terminal'], shape='ellipse')
            legend.node('leg_target', 'Target', fillcolor=self.node_colors['target'], shape='ellipse')
        
        # Render the graph
        try:
            self.graph.render(output_path, cleanup=True)
            print(f"✅ Visualization saved as {output_path}.png")
            return f"{output_path}.png"
        except Exception as e:
            print(f"❌ Error rendering graph: {e}")
            return None
    
    def visualize_from_tree_object(self, tree_root, output_path: str = "synthesis_tree_obj"):
        """
        Visualize synthesis tree from tree node objects.
        
        Args:
            tree_root: Root TreeNode object
            output_path: Output file path (without extension)
        """
        self.create_graph(f"Tree Structure - {getattr(tree_root, 'smiles', 'Unknown')}")
        
        visited = set()
        
        def add_node_recursive(node, parent_id=None):
            if node.node_id in visited:
                return
            visited.add(node.node_id)
            
            # Determine node type and properties
            if hasattr(node, 'smiles'):  # MoleculeNode
                node_type = 'molecule'
                if hasattr(node, 'status') and str(node.status) == 'NodeStatus.TERMINAL':
                    node_type = 'terminal'
                elif parent_id is None:  # Root node
                    node_type = 'target'
                
                label = f"{node_type.upper()}\\n{node.smiles}\\nID: {node.node_id[:8]}..."
                shape = 'ellipse'
                color = self.node_colors[node_type]
                
            else:  # ReactionNode
                reaction_string = node.reaction_data.get('rxn_string', 'N/A')
                label = f"REACTION\\n{reaction_string}\\nID: {node.node_id[:8]}..."
                shape = 'box'
                color = self.node_colors['reaction']
            
            self.graph.node(node.node_id, label=label, fillcolor=color, shape=shape)
            
            # Add edge from parent
            if parent_id:
                self.graph.edge(parent_id, node.node_id, color=self.edge_colors['tree_parent'])
            
            # Recursively add children
            for child in getattr(node, 'children', []):
                add_node_recursive(child, node.node_id)
        
        add_node_recursive(tree_root)
        
        # Render the graph
        try:
            self.graph.render(output_path, cleanup=True)
            print(f"✅ Tree visualization saved as {output_path}.png")
            return f"{output_path}.png"
        except Exception as e:
            print(f"❌ Error rendering graph: {e}")
            return None

def main():
    parser = argparse.ArgumentParser(description='Visualize synthesis trees')
    parser.add_argument('--db-data', type=str, help='JSON file containing database route data')
    parser.add_argument('--tree-object', type=str, help='Pickle file containing tree root object')
    parser.add_argument('--output', type=str, default='synthesis_tree', help='Output file name (without extension)')
    
    args = parser.parse_args()
    
    if not args.db_data and not args.tree_object:
        print("❌ Please provide either --db-data or --tree-object")
        parser.print_help()
        sys.exit(1)
    
    visualizer = SynthesisTreeVisualizer()
    
    if args.db_data:
        if not Path(args.db_data).exists():
            print(f"❌ File not found: {args.db_data}")
            sys.exit(1)
        
        try:
            with open(args.db_data, 'r') as f:
                db_data = json.load(f)
            
            result = visualizer.visualize_from_db_data(db_data, args.output)
            if result:
                print(f"📊 Database visualization complete: {result}")
        
        except Exception as e:
            print(f"❌ Error processing database data: {e}")
            sys.exit(1)
    
    elif args.tree_object:
        if not Path(args.tree_object).exists():
            print(f"❌ File not found: {args.tree_object}")
            sys.exit(1)
        
        try:
            with open(args.tree_object, 'rb') as f:
                tree_root = pickle.load(f)
            
            result = visualizer.visualize_from_tree_object(tree_root, args.output)
            if result:
                print(f"🌳 Tree visualization complete: {result}")
        
        except Exception as e:
            print(f"❌ Error processing tree object: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main()
