#!/usr/bin/env python3
"""
Launch script for the Modern Synthesis Tree Visualizer
"""

import sys
import subprocess
import os

def check_dependencies():
    """Check if required dependencies are installed."""
    try:
        import flask
        import pymongo
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("📦 Installing dependencies...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "modern_requirements.txt"])
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            return False

def main():
    """Main function to launch the visualizer."""
    print("🚀 Modern Synthesis Tree Visualizer")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        print("❌ Please install dependencies manually:")
        print("   pip install Flask pymongo")
        return
    
    # Import and run the visualizer
    try:
        from modern_visualizer import run_server
        print("✅ Dependencies OK")
        print("🌐 Starting web server...")
        print("📊 Features:")
        print("   • Beautiful D3.js interactive graphs")
        print("   • Modern responsive UI")
        print("   • Hover tooltips with detailed info")
        print("   • Zoom, pan, and drag interactions")
        print("   • Export to PNG functionality")
        print("   • Real-time MongoDB integration")
        print()
        
        run_server(debug=False, open_browser_flag=True)
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure modern_visualizer.py is in the current directory")
    except KeyboardInterrupt:
        print("\n👋 Shutting down visualizer...")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
