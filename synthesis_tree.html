<html>
    <head>
        <meta charset="utf-8">
        
            <script src="lib/bindings/utils.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 800px;
                 background-color: #ffffff;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             
             #loadingBar {
                 position:absolute;
                 top:0px;
                 left:0px;
                 width: 100%;
                 height: 800px;
                 background-color:rgba(200,200,200,0.8);
                 -webkit-transition: all 0.5s ease;
                 -moz-transition: all 0.5s ease;
                 -ms-transition: all 0.5s ease;
                 -o-transition: all 0.5s ease;
                 transition: all 0.5s ease;
                 opacity:1;
             }

             #bar {
                 position:absolute;
                 top:0px;
                 left:0px;
                 width:20px;
                 height:20px;
                 margin:auto auto auto auto;
                 border-radius:11px;
                 border:2px solid rgba(30,30,30,0.05);
                 background: rgb(0, 173, 246); /* Old browsers */
                 box-shadow: 2px 0px 4px rgba(0,0,0,0.4);
             }

             #border {
                 position:absolute;
                 top:10px;
                 left:10px;
                 width:500px;
                 height:23px;
                 margin:auto auto auto auto;
                 box-shadow: 0px 0px 4px rgba(0,0,0,0.2);
                 border-radius:10px;
             }

             #text {
                 position:absolute;
                 top:8px;
                 left:530px;
                 width:30px;
                 height:50px;
                 margin:auto auto auto auto;
                 font-size:22px;
                 color: #000000;
             }

             div.outerBorder {
                 position:relative;
                 top:400px;
                 width:600px;
                 height:44px;
                 margin:auto auto auto auto;
                 border:8px solid rgba(0,0,0,0.1);
                 background: rgb(252,252,252); /* Old browsers */
                 background: -moz-linear-gradient(top,  rgba(252,252,252,1) 0%, rgba(237,237,237,1) 100%); /* FF3.6+ */
                 background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(252,252,252,1)), color-stop(100%,rgba(237,237,237,1))); /* Chrome,Safari4+ */
                 background: -webkit-linear-gradient(top,  rgba(252,252,252,1) 0%,rgba(237,237,237,1) 100%); /* Chrome10+,Safari5.1+ */
                 background: -o-linear-gradient(top,  rgba(252,252,252,1) 0%,rgba(237,237,237,1) 100%); /* Opera 11.10+ */
                 background: -ms-linear-gradient(top,  rgba(252,252,252,1) 0%,rgba(237,237,237,1) 100%); /* IE10+ */
                 background: linear-gradient(to bottom,  rgba(252,252,252,1) 0%,rgba(237,237,237,1) 100%); /* W3C */
                 filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fcfcfc', endColorstr='#ededed',GradientType=0 ); /* IE6-9 */
                 border-radius:72px;
                 box-shadow: 0px 0px 10px rgba(0,0,0,0.2);
             }
             

             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
            <div id="loadingBar">
              <div class="outerBorder">
                <div id="text">0%</div>
                <div id="border">
                  <div id="bar"></div>
                </div>
              </div>
            </div>
        
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#FF5722", "font": {"color": "black"}, "id": "target_1", "label": "TARGET 1\nClc1ccccn1", "shape": "ellipse", "size": 25, "title": "Route 1 Target: Clc1ccccn1"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_1_a760137d-269a-4c69-a778-71ad7f3dcd40", "label": "R1-S1", "shape": "box", "size": 20, "title": "Route 1, Step 1: ClCl.Clc1ccccn1\u003eClC(Cl)Cl\u003eClc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_1_ae79259b-8de4-45f4-921b-d24e03c6a588", "label": "R1-S2", "shape": "box", "size": 20, "title": "Route 1, Step 2: Clc1cccc(Cl)n1\u003eC1CCOC1.[Al+3].[H-].[H-].[H-].[H-].[Li+]\u003eClc1ccccn1"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "target_2", "label": "TARGET 2\nClc1cccnc1Br", "shape": "ellipse", "size": 25, "title": "Route 2 Target: Clc1cccnc1Br"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_2_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8", "label": "R2-S1", "shape": "box", "size": 20, "title": "Route 2, Step 1: Clc1cccnc1Br\u003eC1CCOC1.CC(C)[Mg]Cl\u003eClc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_2_b8dac19e-2fca-4c48-ae92-d14fe5de4f09", "label": "R2-S2", "shape": "box", "size": 20, "title": "Route 2, Step 2: Nc1cnc(Br)c(Cl)c1\u003eC1CCOC1.CC(C)CCON=O\u003eClc1cccnc1Br"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "target_3", "label": "TARGET 3\nClc1ccccn1", "shape": "ellipse", "size": 25, "title": "Route 3 Target: Clc1ccccn1"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_3_a760137d-269a-4c69-a778-71ad7f3dcd40", "label": "R3-S1", "shape": "box", "size": 20, "title": "Route 3, Step 1: ClCl.Clc1ccccn1\u003eClC(Cl)Cl\u003eClc1cccnc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_3_47466ce1-f45c-42f7-8481-1c90450b478a", "label": "R3-S2", "shape": "box", "size": 20, "title": "Route 3, Step 2: COc1ccccn1\u003eBr\u003eClc1ccccn1"}, {"color": "#009688", "font": {"color": "black"}, "id": "target_4", "label": "TARGET 4\nClc1ccccn1", "shape": "ellipse", "size": 25, "title": "Route 4 Target: Clc1ccccn1"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_4_a760137d-269a-4c69-a778-71ad7f3dcd40", "label": "R4-S1", "shape": "box", "size": 20, "title": "Route 4, Step 1: ClCl.Clc1ccccn1\u003eClC(Cl)Cl\u003eClc1cccnc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_4_a01e50c3-75dc-4282-b981-708583506a28", "label": "R4-S2", "shape": "box", "size": 20, "title": "Route 4, Step 2: O=C(O)c1cccnc1Cl\u003eCc1ccccc1C\u003eClc1ccccn1"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "target_5", "label": "TARGET 5\nClc1ccccn1", "shape": "ellipse", "size": 25, "title": "Route 5 Target: Clc1ccccn1"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_5_a760137d-269a-4c69-a778-71ad7f3dcd40", "label": "R5-S1", "shape": "box", "size": 20, "title": "Route 5, Step 1: ClCl.Clc1ccccn1\u003eClC(Cl)Cl\u003eClc1cccnc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_5_dd4caa63-019a-4a62-bc90-86263e0f2240", "label": "R5-S2", "shape": "box", "size": 20, "title": "Route 5, Step 2: Clc1ccnc(Cl)c1\u003eO.[Na+].[OH-]\u003eClc1ccccn1"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "target_6", "label": "TARGET 6\nNc1cccnc1Cl", "shape": "ellipse", "size": 25, "title": "Route 6 Target: Nc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_6_60c78db0-0cff-45cb-8a80-03ba83eb381c", "label": "R6-S1", "shape": "box", "size": 20, "title": "Route 6, Step 1: Cl.Nc1cccnc1Cl\u003eO.O=N[O-].[Na+]\u003eClc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_6_5dd8ae92-caa2-4fc9-be62-f399d1220a6c", "label": "R6-S2", "shape": "box", "size": 20, "title": "Route 6, Step 2: Nc1ccc(Cl)nc1Cl\u003e[Pd].CO\u003eNc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "target_7", "label": "TARGET 7\nNc1cccnc1Cl", "shape": "ellipse", "size": 25, "title": "Route 7 Target: Nc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_7_60c78db0-0cff-45cb-8a80-03ba83eb381c", "label": "R7-S1", "shape": "box", "size": 20, "title": "Route 7, Step 1: Cl.Nc1cccnc1Cl\u003eO.O=N[O-].[Na+]\u003eClc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_7_3808ab16-82f2-424c-b2e3-142a582b8228", "label": "R7-S2", "shape": "box", "size": 20, "title": "Route 7, Step 2: O=[N+]([O-])c1cccnc1Cl\u003e[Fe].CC(=O)O\u003eNc1cccnc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "target_8", "label": "TARGET 8\nNc1cccnc1Cl", "shape": "ellipse", "size": 25, "title": "Route 8 Target: Nc1cccnc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_8_60c78db0-0cff-45cb-8a80-03ba83eb381c", "label": "R8-S1", "shape": "box", "size": 20, "title": "Route 8, Step 1: Cl.Nc1cccnc1Cl\u003eO.O=N[O-].[Na+]\u003eClc1cccnc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_8_ead53485-b8ef-4343-a569-6425ff05aea3", "label": "R8-S2", "shape": "box", "size": 20, "title": "Route 8, Step 2: Nc1c(Cl)ncc(Cl)c1Cl\u003e[Pd].CO\u003eNc1cccnc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "target_9", "label": "TARGET 9\nNc1cccnc1Cl", "shape": "ellipse", "size": 25, "title": "Route 9 Target: Nc1cccnc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_9_60c78db0-0cff-45cb-8a80-03ba83eb381c", "label": "R9-S1", "shape": "box", "size": 20, "title": "Route 9, Step 1: Cl.Nc1cccnc1Cl\u003eO.O=N[O-].[Na+]\u003eClc1cccnc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_9_8d854fcc-837d-47ab-b282-10c0c03e64cd", "label": "R9-S2", "shape": "box", "size": 20, "title": "Route 9, Step 2: Nc1cc(Br)cnc1Cl\u003eC1CCOC1.[Li]CCCC\u003eNc1cccnc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "target_10", "label": "TARGET 10\nClc1ccccn1", "shape": "ellipse", "size": 25, "title": "Route 10 Target: Clc1ccccn1"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_10_a760137d-269a-4c69-a778-71ad7f3dcd40", "label": "R10-S1", "shape": "box", "size": 20, "title": "Route 10, Step 1: ClCl.Clc1ccccn1\u003eClC(Cl)Cl\u003eClc1cccnc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_10_076a7656-9468-4698-9f14-cda406c7828f", "label": "R10-S2", "shape": "box", "size": 20, "title": "Route 10, Step 2: c1ccncc1\u003eO.Cl.[Na+].[OH-]\u003eClc1ccccn1"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "target_11", "label": "TARGET 11\nClc1ccccn1", "shape": "ellipse", "size": 25, "title": "Route 11 Target: Clc1ccccn1"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_11_a760137d-269a-4c69-a778-71ad7f3dcd40", "label": "R11-S1", "shape": "box", "size": 20, "title": "Route 11, Step 1: ClCl.Clc1ccccn1\u003eClC(Cl)Cl\u003eClc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_11_6d0a425c-0b19-4801-bbea-13bac240becc", "label": "R11-S2", "shape": "box", "size": 20, "title": "Route 11, Step 2: Nc1cccnc1Cl\u003eC1CCOC1.CC(C)CCON=O\u003eClc1ccccn1"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "target_12", "label": "TARGET 12\nClc1ccccn1", "shape": "ellipse", "size": 25, "title": "Route 12 Target: Clc1ccccn1"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_12_a760137d-269a-4c69-a778-71ad7f3dcd40", "label": "R12-S1", "shape": "box", "size": 20, "title": "Route 12, Step 1: ClCl.Clc1ccccn1\u003eClC(Cl)Cl\u003eClc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_12_d425baa0-6e2f-4c68-a75d-fd0cb8eaf988", "label": "R12-S2", "shape": "box", "size": 20, "title": "Route 12, Step 2: Nc1ccccn1\u003eC1CCOC1.CC(C)CCON=O\u003eClc1ccccn1"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "target_13", "label": "TARGET 13\nClc1ccccn1", "shape": "ellipse", "size": 25, "title": "Route 13 Target: Clc1ccccn1"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_13_a760137d-269a-4c69-a778-71ad7f3dcd40", "label": "R13-S1", "shape": "box", "size": 20, "title": "Route 13, Step 1: ClCl.Clc1ccccn1\u003eClC(Cl)Cl\u003eClc1cccnc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_13_9454447f-b5dd-4e6b-9a05-00a5e0e7adaa", "label": "R13-S2", "shape": "box", "size": 20, "title": "Route 13, Step 2: Cn1ccccc1=O\u003eC1CCOC1.[Al+3].[H-].[H-].[H-].[H-].[Li+]\u003eClc1ccccn1"}, {"color": "#009688", "font": {"color": "black"}, "id": "target_14", "label": "TARGET 14\nClc1ccccn1", "shape": "ellipse", "size": 25, "title": "Route 14 Target: Clc1ccccn1"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_14_a760137d-269a-4c69-a778-71ad7f3dcd40", "label": "R14-S1", "shape": "box", "size": 20, "title": "Route 14, Step 1: ClCl.Clc1ccccn1\u003eClC(Cl)Cl\u003eClc1cccnc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_14_abeb7334-601a-49f7-822a-adec03fa5994", "label": "R14-S2", "shape": "box", "size": 20, "title": "Route 14, Step 2: Oc1ccccn1\u003e[Zn].CC(=O)O\u003eClc1ccccn1"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "target_15", "label": "TARGET 15\nClc1ccccn1", "shape": "ellipse", "size": 25, "title": "Route 15 Target: Clc1ccccn1"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_15_a760137d-269a-4c69-a778-71ad7f3dcd40", "label": "R15-S1", "shape": "box", "size": 20, "title": "Route 15, Step 1: ClCl.Clc1ccccn1\u003eClC(Cl)Cl\u003eClc1cccnc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_15_c10faa5a-5c26-49ce-a4e9-0e3ff160f369", "label": "R15-S2", "shape": "box", "size": 20, "title": "Route 15, Step 2: Cn1ccccc1=O.O=P(Cl)(Cl)Cl\u003eCc1ccccc1\u003eClc1ccccn1"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "target_16", "label": "TARGET 16\nNc1cccnc1Cl", "shape": "ellipse", "size": 25, "title": "Route 16 Target: Nc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_16_60c78db0-0cff-45cb-8a80-03ba83eb381c", "label": "R16-S1", "shape": "box", "size": 20, "title": "Route 16, Step 1: Cl.Nc1cccnc1Cl\u003eO.O=N[O-].[Na+]\u003eClc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_16_b67bb74c-6965-4b14-a072-6c084310660e", "label": "R16-S2", "shape": "box", "size": 20, "title": "Route 16, Step 2: CC(C)(C)OC(=O)Nc1cccnc1Cl\u003eClCCl.O=C(O)C(F)(F)F\u003eNc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "target_17", "label": "TARGET 17\nNc1cccnc1Cl", "shape": "ellipse", "size": 25, "title": "Route 17 Target: Nc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_17_60c78db0-0cff-45cb-8a80-03ba83eb381c", "label": "R17-S1", "shape": "box", "size": 20, "title": "Route 17, Step 1: Cl.Nc1cccnc1Cl\u003eO.O=N[O-].[Na+]\u003eClc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_17_4de17c7b-a464-4704-8bb7-f6963773b7c1", "label": "R17-S2", "shape": "box", "size": 20, "title": "Route 17, Step 2: Nc1ccc[nH]c1=O.O=P(Cl)(Cl)Cl\u003eO.[Na+].[OH-]\u003eNc1cccnc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "target_18", "label": "TARGET 18\nNc1cccnc1Cl", "shape": "ellipse", "size": 25, "title": "Route 18 Target: Nc1cccnc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_18_60c78db0-0cff-45cb-8a80-03ba83eb381c", "label": "R18-S1", "shape": "box", "size": 20, "title": "Route 18, Step 1: Cl.Nc1cccnc1Cl\u003eO.O=N[O-].[Na+]\u003eClc1cccnc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_18_5e899a32-d11c-4b44-b81a-7bfb66624eda", "label": "R18-S2", "shape": "box", "size": 20, "title": "Route 18, Step 2: Nc1cc(Cl)cnc1Cl\u003e[Zn].CC(=O)O\u003eNc1cccnc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "target_19", "label": "TARGET 19\nNc1cccnc1Cl", "shape": "ellipse", "size": 25, "title": "Route 19 Target: Nc1cccnc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_19_60c78db0-0cff-45cb-8a80-03ba83eb381c", "label": "R19-S1", "shape": "box", "size": 20, "title": "Route 19, Step 1: Cl.Nc1cccnc1Cl\u003eO.O=N[O-].[Na+]\u003eClc1cccnc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_19_261ab6a4-d119-4130-a307-86d7fb9e0178", "label": "R19-S2", "shape": "box", "size": 20, "title": "Route 19, Step 2: O=C1c2ccccc2C(=O)N1c1cccnc1Cl\u003eCCO.NN.O\u003eNc1cccnc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "target_20", "label": "TARGET 20\nNc1cccnc1Cl", "shape": "ellipse", "size": 25, "title": "Route 20 Target: Nc1cccnc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_20_60c78db0-0cff-45cb-8a80-03ba83eb381c", "label": "R20-S1", "shape": "box", "size": 20, "title": "Route 20, Step 1: Cl.Nc1cccnc1Cl\u003eO.O=N[O-].[Na+]\u003eClc1cccnc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_20_eb6ea37c-f339-4037-a6b4-ad675114c66b", "label": "R20-S2", "shape": "box", "size": 20, "title": "Route 20, Step 2: CC(=O)Nc1cccnc1Cl\u003eCCO.Cl\u003eNc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "target_21", "label": "TARGET 21\nNc1cccnc1Cl", "shape": "ellipse", "size": 25, "title": "Route 21 Target: Nc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_21_60c78db0-0cff-45cb-8a80-03ba83eb381c", "label": "R21-S1", "shape": "box", "size": 20, "title": "Route 21, Step 1: Cl.Nc1cccnc1Cl\u003eO.O=N[O-].[Na+]\u003eClc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_21_f1362aaf-e888-4ec5-810d-c5da4483c0a7", "label": "R21-S2", "shape": "box", "size": 20, "title": "Route 21, Step 2: [N-]=[N+]=Nc1cccnc1Cl\u003eCO.[BH4-].[Na+]\u003eNc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "target_22", "label": "TARGET 22\nOc1ncccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 22 Target: Oc1ncccc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_22_23711955-48a1-41f7-ab21-c1eaa3b6b307", "label": "R22-S1", "shape": "box", "size": 20, "title": "Route 22, Step 1: O=P(Cl)(Cl)Cl.Oc1ncccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_22_5b7ac276-1222-40f3-850c-84ecefff4018", "label": "R22-S2", "shape": "box", "size": 20, "title": "Route 22, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_22_df703e4b-c31b-4beb-b0fd-d6aa4880e88c", "label": "R22-S3", "shape": "box", "size": 20, "title": "Route 22, Step 3: COc1ncccc1Cl\u003eClCCl.BrB(Br)Br\u003eOc1ncccc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "target_23", "label": "TARGET 23\nOc1ncccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 23 Target: Oc1ncccc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_23_23711955-48a1-41f7-ab21-c1eaa3b6b307", "label": "R23-S1", "shape": "box", "size": 20, "title": "Route 23, Step 1: O=P(Cl)(Cl)Cl.Oc1ncccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_23_5b7ac276-1222-40f3-850c-84ecefff4018", "label": "R23-S2", "shape": "box", "size": 20, "title": "Route 23, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_23_13e11092-04f4-4f6e-ad19-a6aa49d72edb", "label": "R23-S3", "shape": "box", "size": 20, "title": "Route 23, Step 3: Oc1nc(Cl)ccc1Cl\u003eO.Cl.[Na+].[OH-]\u003eOc1ncccc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "target_24", "label": "TARGET 24\nO=c1[nH]cccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 24 Target: O=c1[nH]cccc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_24_a1e81514-8ccf-4571-92ab-51621aef2237", "label": "R24-S1", "shape": "box", "size": 20, "title": "Route 24, Step 1: O=P(Cl)(Cl)Cl.O=c1[nH]cccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_24_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5", "label": "R24-S2", "shape": "box", "size": 20, "title": "Route 24, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_24_5545e7da-5d79-43c4-9609-4ae0d7772e59", "label": "R24-S3", "shape": "box", "size": 20, "title": "Route 24, Step 3: O=c1[nH]c(Cl)ccc1Cl\u003eO.Cl.[Na+].[OH-]\u003eO=c1[nH]cccc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "target_25", "label": "TARGET 25\nOc1ncccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 25 Target: Oc1ncccc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_25_23711955-48a1-41f7-ab21-c1eaa3b6b307", "label": "R25-S1", "shape": "box", "size": 20, "title": "Route 25, Step 1: O=P(Cl)(Cl)Cl.Oc1ncccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_25_5b7ac276-1222-40f3-850c-84ecefff4018", "label": "R25-S2", "shape": "box", "size": 20, "title": "Route 25, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_25_de9dd80b-e0ee-4d26-bae5-618c6ca9f1c9", "label": "R25-S3", "shape": "box", "size": 20, "title": "Route 25, Step 3: Oc1ncc(Cl)cc1Cl\u003eO.Cl.[Na+].[OH-]\u003eOc1ncccc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "target_26", "label": "TARGET 26\nOc1ncccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 26 Target: Oc1ncccc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_26_23711955-48a1-41f7-ab21-c1eaa3b6b307", "label": "R26-S1", "shape": "box", "size": 20, "title": "Route 26, Step 1: O=P(Cl)(Cl)Cl.Oc1ncccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_26_5b7ac276-1222-40f3-850c-84ecefff4018", "label": "R26-S2", "shape": "box", "size": 20, "title": "Route 26, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_26_3ea996b5-0374-4cfd-87ce-40a6bc580d8c", "label": "R26-S3", "shape": "box", "size": 20, "title": "Route 26, Step 3: Oc1nc(Cl)c(Cl)cc1Cl\u003e[Zn].O.[Na+].[OH-]\u003eOc1ncccc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "target_27", "label": "TARGET 27\nOc1ncccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 27 Target: Oc1ncccc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_27_23711955-48a1-41f7-ab21-c1eaa3b6b307", "label": "R27-S1", "shape": "box", "size": 20, "title": "Route 27, Step 1: O=P(Cl)(Cl)Cl.Oc1ncccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_27_5b7ac276-1222-40f3-850c-84ecefff4018", "label": "R27-S2", "shape": "box", "size": 20, "title": "Route 27, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_27_3caf3224-61f4-4af0-9cac-fdf1bb234101", "label": "R27-S3", "shape": "box", "size": 20, "title": "Route 27, Step 3: Nc1ncccc1Cl\u003eCCO.[K+].[OH-]\u003eOc1ncccc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "target_28", "label": "TARGET 28\nO=c1[nH]cccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 28 Target: O=c1[nH]cccc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_28_a1e81514-8ccf-4571-92ab-51621aef2237", "label": "R28-S1", "shape": "box", "size": 20, "title": "Route 28, Step 1: O=P(Cl)(Cl)Cl.O=c1[nH]cccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_28_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5", "label": "R28-S2", "shape": "box", "size": 20, "title": "Route 28, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_28_5ee74128-4694-4252-b1ad-25402b002e06", "label": "R28-S3", "shape": "box", "size": 20, "title": "Route 28, Step 3: O=C(O)c1c[nH]c(=O)c(Cl)c1\u003eO.c1ccc2ncccc2c1\u003eO=c1[nH]cccc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "target_29", "label": "TARGET 29\nOc1ncccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 29 Target: Oc1ncccc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_29_23711955-48a1-41f7-ab21-c1eaa3b6b307", "label": "R29-S1", "shape": "box", "size": 20, "title": "Route 29, Step 1: O=P(Cl)(Cl)Cl.Oc1ncccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_29_5b7ac276-1222-40f3-850c-84ecefff4018", "label": "R29-S2", "shape": "box", "size": 20, "title": "Route 29, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_29_720d172a-fb08-4f5c-8c69-9a9827388318", "label": "R29-S3", "shape": "box", "size": 20, "title": "Route 29, Step 3: O=C(O)c1cnc(O)c(Cl)c1\u003eCc1ccccc1C\u003eOc1ncccc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "target_30", "label": "TARGET 30\nOc1ncccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 30 Target: Oc1ncccc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_30_23711955-48a1-41f7-ab21-c1eaa3b6b307", "label": "R30-S1", "shape": "box", "size": 20, "title": "Route 30, Step 1: O=P(Cl)(Cl)Cl.Oc1ncccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_30_5b7ac276-1222-40f3-850c-84ecefff4018", "label": "R30-S2", "shape": "box", "size": 20, "title": "Route 30, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_30_da3d451c-ee85-4c67-8c64-864cf9a6bd6b", "label": "R30-S3", "shape": "box", "size": 20, "title": "Route 30, Step 3: C[Si](C)(C)CCOc1ncccc1Cl\u003eC1CCOC1.CCCC[N+](CCCC)(CCCC)CCCC.[F-]\u003eOc1ncccc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "target_31", "label": "TARGET 31\nClc1cccnc1Br", "shape": "ellipse", "size": 25, "title": "Route 31 Target: Clc1cccnc1Br"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_31_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8", "label": "R31-S1", "shape": "box", "size": 20, "title": "Route 31, Step 1: Clc1cccnc1Br\u003eC1CCOC1.CC(C)[Mg]Cl\u003eClc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_31_5c24aefa-e554-4283-99bb-5ef1952e6d52", "label": "R31-S2", "shape": "box", "size": 20, "title": "Route 31, Step 2: Nc1ncccc1Cl\u003eC1CCOC1.CC(C)CCON=O\u003eClc1cccnc1Br"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "target_32", "label": "TARGET 32\nO=c1[nH]cccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 32 Target: O=c1[nH]cccc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_32_a1e81514-8ccf-4571-92ab-51621aef2237", "label": "R32-S1", "shape": "box", "size": 20, "title": "Route 32, Step 1: O=P(Cl)(Cl)Cl.O=c1[nH]cccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_32_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5", "label": "R32-S2", "shape": "box", "size": 20, "title": "Route 32, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_32_d7ad5f08-43f1-4124-9b11-a09ddfbe0291", "label": "R32-S3", "shape": "box", "size": 20, "title": "Route 32, Step 3: ClCl.Oc1ccccn1\u003eO.[Na+].[OH-]\u003eO=c1[nH]cccc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "target_33", "label": "TARGET 33\nOc1ncccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 33 Target: Oc1ncccc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_33_23711955-48a1-41f7-ab21-c1eaa3b6b307", "label": "R33-S1", "shape": "box", "size": 20, "title": "Route 33, Step 1: O=P(Cl)(Cl)Cl.Oc1ncccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_33_5b7ac276-1222-40f3-850c-84ecefff4018", "label": "R33-S2", "shape": "box", "size": 20, "title": "Route 33, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_33_a774b7b9-6ffa-4670-b82b-0d6d8864cc7b", "label": "R33-S3", "shape": "box", "size": 20, "title": "Route 33, Step 3: Clc1cccnc1Br\u003eC1CCOC1.[Li]CCCC\u003eOc1ncccc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "target_34", "label": "TARGET 34\nO=c1[nH]cccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 34 Target: O=c1[nH]cccc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_34_a1e81514-8ccf-4571-92ab-51621aef2237", "label": "R34-S1", "shape": "box", "size": 20, "title": "Route 34, Step 1: O=P(Cl)(Cl)Cl.O=c1[nH]cccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_34_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5", "label": "R34-S2", "shape": "box", "size": 20, "title": "Route 34, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_34_3a014dd3-430c-4e34-ba14-c98f755ced31", "label": "R34-S3", "shape": "box", "size": 20, "title": "Route 34, Step 3: O=C(O)c1ccc(Cl)c(=O)[nH]1\u003ec1ccc(Oc2ccccc2)cc1\u003eO=c1[nH]cccc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "target_35", "label": "TARGET 35\nO=c1[nH]cccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 35 Target: O=c1[nH]cccc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_35_a1e81514-8ccf-4571-92ab-51621aef2237", "label": "R35-S1", "shape": "box", "size": 20, "title": "Route 35, Step 1: O=P(Cl)(Cl)Cl.O=c1[nH]cccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_35_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5", "label": "R35-S2", "shape": "box", "size": 20, "title": "Route 35, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_35_a2b8cc85-8024-4b0e-b7ef-7625767d2e9e", "label": "R35-S3", "shape": "box", "size": 20, "title": "Route 35, Step 3: COc1ccc(Cn2cccc(Cl)c2=O)cc1\u003eO=C(O)C(F)(F)F.COc1ccccc1\u003eO=c1[nH]cccc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "target_36", "label": "TARGET 36\nO=c1[nH]cccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 36 Target: O=c1[nH]cccc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_36_a1e81514-8ccf-4571-92ab-51621aef2237", "label": "R36-S1", "shape": "box", "size": 20, "title": "Route 36, Step 1: O=P(Cl)(Cl)Cl.O=c1[nH]cccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_36_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5", "label": "R36-S2", "shape": "box", "size": 20, "title": "Route 36, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_36_c607e3fe-0dc9-4f2a-bb91-0211b1bf2d2f", "label": "R36-S3", "shape": "box", "size": 20, "title": "Route 36, Step 3: O=c1[nH]c(Cl)c(Cl)cc1Cl\u003eO\u003eO=c1[nH]cccc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "target_37", "label": "TARGET 37\nO=c1[nH]cccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 37 Target: O=c1[nH]cccc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_37_a1e81514-8ccf-4571-92ab-51621aef2237", "label": "R37-S1", "shape": "box", "size": 20, "title": "Route 37, Step 1: O=P(Cl)(Cl)Cl.O=c1[nH]cccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_37_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5", "label": "R37-S2", "shape": "box", "size": 20, "title": "Route 37, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_37_e7b91c84-b455-4ff5-a53e-4f9e57da75bc", "label": "R37-S3", "shape": "box", "size": 20, "title": "Route 37, Step 3: Nn1cccc(Cl)c1=O\u003eO.Cl.[Na+].[OH-]\u003eO=c1[nH]cccc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "target_38", "label": "TARGET 38\nO=c1[nH]cccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 38 Target: O=c1[nH]cccc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_38_a1e81514-8ccf-4571-92ab-51621aef2237", "label": "R38-S1", "shape": "box", "size": 20, "title": "Route 38, Step 1: O=P(Cl)(Cl)Cl.O=c1[nH]cccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_38_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5", "label": "R38-S2", "shape": "box", "size": 20, "title": "Route 38, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_38_3b8f87b4-88b8-432f-952c-3364430d517f", "label": "R38-S3", "shape": "box", "size": 20, "title": "Route 38, Step 3: O=c1cccc[nH]1\u003eO.Cl.[Na+].[OH-]\u003eO=c1[nH]cccc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "target_39", "label": "TARGET 39\nClc1cccnc1Br", "shape": "ellipse", "size": 25, "title": "Route 39 Target: Clc1cccnc1Br"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_39_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8", "label": "R39-S1", "shape": "box", "size": 20, "title": "Route 39, Step 1: Clc1cccnc1Br\u003eC1CCOC1.CC(C)[Mg]Cl\u003eClc1cccnc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_39_e17fc251-3511-43ab-8cb2-a2d223920ce8", "label": "R39-S2", "shape": "box", "size": 20, "title": "Route 39, Step 2: Nc1cccnc1Br\u003eC1CCOC1.CC(C)CCON=O\u003eClc1cccnc1Br"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "target_40", "label": "TARGET 40\nO=c1[nH]cccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 40 Target: O=c1[nH]cccc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_40_a1e81514-8ccf-4571-92ab-51621aef2237", "label": "R40-S1", "shape": "box", "size": 20, "title": "Route 40, Step 1: O=P(Cl)(Cl)Cl.O=c1[nH]cccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_40_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5", "label": "R40-S2", "shape": "box", "size": 20, "title": "Route 40, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_40_f3f70f30-b9a6-4011-b715-8694c4d484ac", "label": "R40-S3", "shape": "box", "size": 20, "title": "Route 40, Step 3: O=c1[nH]cc(Cl)cc1Cl\u003eO.Cl.[Na+].[OH-]\u003eO=c1[nH]cccc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "target_41", "label": "TARGET 41\nO=c1[nH]cccc1Cl", "shape": "ellipse", "size": 25, "title": "Route 41 Target: O=c1[nH]cccc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_41_a1e81514-8ccf-4571-92ab-51621aef2237", "label": "R41-S1", "shape": "box", "size": 20, "title": "Route 41, Step 1: O=P(Cl)(Cl)Cl.O=c1[nH]cccc1Cl\u003eO\u003eClc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_41_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5", "label": "R41-S2", "shape": "box", "size": 20, "title": "Route 41, Step 2: O=P(Cl)(Cl)c1ccccc1\u003eClC(Cl)(Cl)Cl\u003eO=P(Cl)(Cl)Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_41_e0bec78f-4c17-41c4-b040-1b3ac9c7e67e", "label": "R41-S3", "shape": "box", "size": 20, "title": "Route 41, Step 3: CCOC(=O)c1c[nH]c(=O)c(Cl)c1\u003eO.Cl.[Na+].[OH-]\u003eO=c1[nH]cccc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "target_42", "label": "TARGET 42\nClc1cccnc1Br", "shape": "ellipse", "size": 25, "title": "Route 42 Target: Clc1cccnc1Br"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_42_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8", "label": "R42-S1", "shape": "box", "size": 20, "title": "Route 42, Step 1: Clc1cccnc1Br\u003eC1CCOC1.CC(C)[Mg]Cl\u003eClc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_42_41d6b800-6b3d-47ba-bc21-1ad22f975fdb", "label": "R42-S2", "shape": "box", "size": 20, "title": "Route 42, Step 2: Clc1cccnc1\u003eO.Cl.[Na+].[OH-]\u003eClc1cccnc1Br"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "target_43", "label": "TARGET 43\nClc1cccnc1Br", "shape": "ellipse", "size": 25, "title": "Route 43 Target: Clc1cccnc1Br"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_43_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8", "label": "R43-S1", "shape": "box", "size": 20, "title": "Route 43, Step 1: Clc1cccnc1Br\u003eC1CCOC1.CC(C)[Mg]Cl\u003eClc1cccnc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_43_f8a64c3f-afd2-4b65-9124-b4d196cf88f9", "label": "R43-S2", "shape": "box", "size": 20, "title": "Route 43, Step 2: BrBr.Clc1cccnc1\u003eCC(=O)O\u003eClc1cccnc1Br"}, {"color": "#009688", "font": {"color": "black"}, "id": "target_44", "label": "TARGET 44\nClc1cccnc1Br", "shape": "ellipse", "size": 25, "title": "Route 44 Target: Clc1cccnc1Br"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_44_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8", "label": "R44-S1", "shape": "box", "size": 20, "title": "Route 44, Step 1: Clc1cccnc1Br\u003eC1CCOC1.CC(C)[Mg]Cl\u003eClc1cccnc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_44_36b23191-3d86-4565-9a3a-91b723410957", "label": "R44-S2", "shape": "box", "size": 20, "title": "Route 44, Step 2: Nc1ccnc(Br)c1Cl\u003eC1CCOC1.CC(C)CCON=O\u003eClc1cccnc1Br"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "target_45", "label": "TARGET 45\nClc1cccnc1Br", "shape": "ellipse", "size": 25, "title": "Route 45 Target: Clc1cccnc1Br"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_45_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8", "label": "R45-S1", "shape": "box", "size": 20, "title": "Route 45, Step 1: Clc1cccnc1Br\u003eC1CCOC1.CC(C)[Mg]Cl\u003eClc1cccnc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_45_1e2d54e9-6500-4554-b22d-ed26f488f75a", "label": "R45-S2", "shape": "box", "size": 20, "title": "Route 45, Step 2: Oc1ncccc1Cl\u003eC1CCOC1.CC(C)OC(=O)N=NC(=O)OC(C)C.c1ccc(P(c2ccccc2)c2ccccc2)cc1\u003eClc1cccnc1Br"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "target_46", "label": "TARGET 46\nClc1cccnc1Br", "shape": "ellipse", "size": 25, "title": "Route 46 Target: Clc1cccnc1Br"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_46_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8", "label": "R46-S1", "shape": "box", "size": 20, "title": "Route 46, Step 1: Clc1cccnc1Br\u003eC1CCOC1.CC(C)[Mg]Cl\u003eClc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_46_de869558-a975-4749-a3ad-0deaef5d0b33", "label": "R46-S2", "shape": "box", "size": 20, "title": "Route 46, Step 2: Clc1c(Br)ccnc1Br\u003eC1CCOC1.CC(C)[Mg]Cl\u003eClc1cccnc1Br"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "target_47", "label": "TARGET 47\nClc1cccnc1Br", "shape": "ellipse", "size": 25, "title": "Route 47 Target: Clc1cccnc1Br"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_47_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8", "label": "R47-S1", "shape": "box", "size": 20, "title": "Route 47, Step 1: Clc1cccnc1Br\u003eC1CCOC1.CC(C)[Mg]Cl\u003eClc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_47_8e0a74b3-bd5c-47e2-aee1-47cfc48e5369", "label": "R47-S2", "shape": "box", "size": 20, "title": "Route 47, Step 2: BrP(Br)Br.Oc1ncccc1Cl\u003eCN(C)C=O.[Na+].[OH-]\u003eClc1cccnc1Br"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "target_48", "label": "TARGET 48\nClc1cc(Cl)c(Cl)nc1Cl", "shape": "ellipse", "size": 25, "title": "Route 48 Target: Clc1cc(Cl)c(Cl)nc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_48_0bcc5f2b-cdfc-48fc-8d17-9a5cbd6f595f", "label": "R48-S1", "shape": "box", "size": 20, "title": "Route 48, Step 1: Clc1cc(Cl)c(Cl)nc1Cl\u003e[Zn]\u003eClc1cccnc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_48_38abce4b-16c4-4dfa-8762-8f6015abd4a2", "label": "R48-S2", "shape": "box", "size": 20, "title": "Route 48, Step 2: Clc1nc(Cl)c(Cl)c(Cl)c1Cl.O\u003eO.[Na+].[OH-]\u003eClc1cc(Cl)c(Cl)nc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "target_49", "label": "TARGET 49\nNc1cccnc1Cl", "shape": "ellipse", "size": 25, "title": "Route 49 Target: Nc1cccnc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_49_0a7f2ce5-c694-4f41-8aec-f31ce1744f32", "label": "R49-S1", "shape": "box", "size": 20, "title": "Route 49, Step 1: Nc1cccnc1Cl\u003eC1CCOC1.CC(C)CCON=O\u003eClc1cccnc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_49_c25049e8-e1a3-4e9a-9423-cb48ec9a6c69", "label": "R49-S2", "shape": "box", "size": 20, "title": "Route 49, Step 2: Nc1ccc(Cl)nc1Cl\u003e[Pd].CO\u003eNc1cccnc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "target_50", "label": "TARGET 50\nNc1cccnc1Cl", "shape": "ellipse", "size": 25, "title": "Route 50 Target: Nc1cccnc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_50_0a7f2ce5-c694-4f41-8aec-f31ce1744f32", "label": "R50-S1", "shape": "box", "size": 20, "title": "Route 50, Step 1: Nc1cccnc1Cl\u003eC1CCOC1.CC(C)CCON=O\u003eClc1cccnc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_50_6ed4f57c-6442-45e4-b1f0-d30b23795ebc", "label": "R50-S2", "shape": "box", "size": 20, "title": "Route 50, Step 2: O=[N+]([O-])c1cccnc1Cl\u003e[Fe].CC(=O)O\u003eNc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "target_51", "label": "TARGET 51\nClc1cc(Cl)c(Cl)nc1Cl", "shape": "ellipse", "size": 25, "title": "Route 51 Target: Clc1cc(Cl)c(Cl)nc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_51_0bcc5f2b-cdfc-48fc-8d17-9a5cbd6f595f", "label": "R51-S1", "shape": "box", "size": 20, "title": "Route 51, Step 1: Clc1cc(Cl)c(Cl)nc1Cl\u003e[Zn]\u003eClc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_51_922cc712-76cb-4e43-9685-b53cf1d3e44e", "label": "R51-S2", "shape": "box", "size": 20, "title": "Route 51, Step 2: Clc1ccc(Cl)c(Cl)n1.Clc1cccc(Cl)n1.Clc1nc(Cl)c(Cl)c(Cl)c1Cl.NN.[Al+3].[HH].[HH].[HH].[HH].[Li+]\u003eCC#N.[Na+].[OH-]\u003eClc1cc(Cl)c(Cl)nc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "target_52", "label": "TARGET 52\nNc1cccnc1Cl", "shape": "ellipse", "size": 25, "title": "Route 52 Target: Nc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_52_0a7f2ce5-c694-4f41-8aec-f31ce1744f32", "label": "R52-S1", "shape": "box", "size": 20, "title": "Route 52, Step 1: Nc1cccnc1Cl\u003eC1CCOC1.CC(C)CCON=O\u003eClc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_52_0dd8141a-b323-47d6-8756-fb2d51f043fb", "label": "R52-S2", "shape": "box", "size": 20, "title": "Route 52, Step 2: Nc1c(Cl)ncc(Cl)c1Cl\u003e[Pd].CO\u003eNc1cccnc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "target_53", "label": "TARGET 53\nClc1ccc(Cl)c(Cl)n1", "shape": "ellipse", "size": 25, "title": "Route 53 Target: Clc1ccc(Cl)c(Cl)n1"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_53_f6fa3cd5-de1e-4999-bf37-81a3ebd1f7ac", "label": "R53-S1", "shape": "box", "size": 20, "title": "Route 53, Step 1: Clc1ccc(Cl)c(Cl)n1\u003e[Zn].CC(=O)O\u003eClc1cccnc1Cl"}, {"color": "#3F51B5", "font": {"color": "black"}, "id": "route_53_e3cdc0b2-39f0-4d45-ac33-34858cfb62fc", "label": "R53-S2", "shape": "box", "size": 20, "title": "Route 53, Step 2: Clc1cc(Cl)c(Cl)c(Cl)n1\u003e[Zn]\u003eClc1ccc(Cl)c(Cl)n1"}, {"color": "#009688", "font": {"color": "black"}, "id": "target_54", "label": "TARGET 54\nNc1ccc(Cl)c(Cl)n1", "shape": "ellipse", "size": 25, "title": "Route 54 Target: Nc1ccc(Cl)c(Cl)n1"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_54_7d470b93-723b-43fa-aab6-da5a3d1b94a3", "label": "R54-S1", "shape": "box", "size": 20, "title": "Route 54, Step 1: Nc1ccc(Cl)c(Cl)n1\u003eC1CCOC1.CC(C)CCON=O\u003eClc1cccnc1Cl"}, {"color": "#009688", "font": {"color": "black"}, "id": "route_54_45282b60-5206-47c6-934a-9d5fdf38edbe", "label": "R54-S2", "shape": "box", "size": 20, "title": "Route 54, Step 2: Nc1cccc(Cl)n1.O=C1CCC(=O)N1Cl\u003eCC#N\u003eNc1ccc(Cl)c(Cl)n1"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "target_55", "label": "TARGET 55\nClc1ccc(Cl)c(Cl)n1", "shape": "ellipse", "size": 25, "title": "Route 55 Target: Clc1ccc(Cl)c(Cl)n1"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_55_f6fa3cd5-de1e-4999-bf37-81a3ebd1f7ac", "label": "R55-S1", "shape": "box", "size": 20, "title": "Route 55, Step 1: Clc1ccc(Cl)c(Cl)n1\u003e[Zn].CC(=O)O\u003eClc1cccnc1Cl"}, {"color": "#FF9800", "font": {"color": "black"}, "id": "route_55_0d03470c-a8f4-4a26-8014-4f8fa0c0ec7d", "label": "R55-S2", "shape": "box", "size": 20, "title": "Route 55, Step 2: Clc1nc(Cl)c(Cl)c(Cl)c1Cl\u003e[Zn]\u003eClc1ccc(Cl)c(Cl)n1"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "target_56", "label": "TARGET 56\nNc1ccc(Cl)c(Cl)n1", "shape": "ellipse", "size": 25, "title": "Route 56 Target: Nc1ccc(Cl)c(Cl)n1"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_56_7d470b93-723b-43fa-aab6-da5a3d1b94a3", "label": "R56-S1", "shape": "box", "size": 20, "title": "Route 56, Step 1: Nc1ccc(Cl)c(Cl)n1\u003eC1CCOC1.CC(C)CCON=O\u003eClc1cccnc1Cl"}, {"color": "#FF5722", "font": {"color": "black"}, "id": "route_56_d48c4cbc-0df6-41da-82ef-c08c5d0ed782", "label": "R56-S2", "shape": "box", "size": 20, "title": "Route 56, Step 2: Nc1ccc(Cl)cn1.O=C1CCC(=O)N1Cl\u003eCC#N\u003eNc1ccc(Cl)c(Cl)n1"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "target_57", "label": "TARGET 57\nClc1cc(Cl)c(Cl)c(Cl)n1", "shape": "ellipse", "size": 25, "title": "Route 57 Target: Clc1cc(Cl)c(Cl)c(Cl)n1"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_57_5540a168-a17e-4ce3-909e-def1dae804da", "label": "R57-S1", "shape": "box", "size": 20, "title": "Route 57, Step 1: Clc1cc(Cl)c(Cl)c(Cl)n1\u003e[Zn]\u003eClc1cccnc1Cl"}, {"color": "#9C27B0", "font": {"color": "black"}, "id": "route_57_dc1f62d7-d183-4b20-b544-12463667b307", "label": "R57-S2", "shape": "box", "size": 20, "title": "Route 57, Step 2: ClCl.Clc1ccc(Cl)c(Cl)n1\u003eCC(=O)O\u003eClc1cc(Cl)c(Cl)c(Cl)n1"}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "#FF5722", "from": "target_1", "to": "route_1_a760137d-269a-4c69-a778-71ad7f3dcd40"}, {"arrows": "to", "color": "#FF5722", "from": "target_1", "to": "route_1_ae79259b-8de4-45f4-921b-d24e03c6a588"}, {"arrows": "to", "color": "#9C27B0", "from": "target_2", "to": "route_2_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8"}, {"arrows": "to", "color": "#9C27B0", "from": "target_2", "to": "route_2_b8dac19e-2fca-4c48-ae92-d14fe5de4f09"}, {"arrows": "to", "color": "#3F51B5", "from": "target_3", "to": "route_3_a760137d-269a-4c69-a778-71ad7f3dcd40"}, {"arrows": "to", "color": "#3F51B5", "from": "target_3", "to": "route_3_47466ce1-f45c-42f7-8481-1c90450b478a"}, {"arrows": "to", "color": "#009688", "from": "target_4", "to": "route_4_a760137d-269a-4c69-a778-71ad7f3dcd40"}, {"arrows": "to", "color": "#009688", "from": "target_4", "to": "route_4_a01e50c3-75dc-4282-b981-708583506a28"}, {"arrows": "to", "color": "#FF9800", "from": "target_5", "to": "route_5_a760137d-269a-4c69-a778-71ad7f3dcd40"}, {"arrows": "to", "color": "#FF9800", "from": "target_5", "to": "route_5_dd4caa63-019a-4a62-bc90-86263e0f2240"}, {"arrows": "to", "color": "#FF5722", "from": "target_6", "to": "route_6_60c78db0-0cff-45cb-8a80-03ba83eb381c"}, {"arrows": "to", "color": "#FF5722", "from": "target_6", "to": "route_6_5dd8ae92-caa2-4fc9-be62-f399d1220a6c"}, {"arrows": "to", "color": "#9C27B0", "from": "target_7", "to": "route_7_60c78db0-0cff-45cb-8a80-03ba83eb381c"}, {"arrows": "to", "color": "#9C27B0", "from": "target_7", "to": "route_7_3808ab16-82f2-424c-b2e3-142a582b8228"}, {"arrows": "to", "color": "#3F51B5", "from": "target_8", "to": "route_8_60c78db0-0cff-45cb-8a80-03ba83eb381c"}, {"arrows": "to", "color": "#3F51B5", "from": "target_8", "to": "route_8_ead53485-b8ef-4343-a569-6425ff05aea3"}, {"arrows": "to", "color": "#009688", "from": "target_9", "to": "route_9_60c78db0-0cff-45cb-8a80-03ba83eb381c"}, {"arrows": "to", "color": "#009688", "from": "target_9", "to": "route_9_8d854fcc-837d-47ab-b282-10c0c03e64cd"}, {"arrows": "to", "color": "#FF9800", "from": "target_10", "to": "route_10_a760137d-269a-4c69-a778-71ad7f3dcd40"}, {"arrows": "to", "color": "#FF9800", "from": "target_10", "to": "route_10_076a7656-9468-4698-9f14-cda406c7828f"}, {"arrows": "to", "color": "#FF5722", "from": "target_11", "to": "route_11_a760137d-269a-4c69-a778-71ad7f3dcd40"}, {"arrows": "to", "color": "#FF5722", "from": "target_11", "to": "route_11_6d0a425c-0b19-4801-bbea-13bac240becc"}, {"arrows": "to", "color": "#9C27B0", "from": "target_12", "to": "route_12_a760137d-269a-4c69-a778-71ad7f3dcd40"}, {"arrows": "to", "color": "#9C27B0", "from": "target_12", "to": "route_12_d425baa0-6e2f-4c68-a75d-fd0cb8eaf988"}, {"arrows": "to", "color": "#3F51B5", "from": "target_13", "to": "route_13_a760137d-269a-4c69-a778-71ad7f3dcd40"}, {"arrows": "to", "color": "#3F51B5", "from": "target_13", "to": "route_13_9454447f-b5dd-4e6b-9a05-00a5e0e7adaa"}, {"arrows": "to", "color": "#009688", "from": "target_14", "to": "route_14_a760137d-269a-4c69-a778-71ad7f3dcd40"}, {"arrows": "to", "color": "#009688", "from": "target_14", "to": "route_14_abeb7334-601a-49f7-822a-adec03fa5994"}, {"arrows": "to", "color": "#FF9800", "from": "target_15", "to": "route_15_a760137d-269a-4c69-a778-71ad7f3dcd40"}, {"arrows": "to", "color": "#FF9800", "from": "target_15", "to": "route_15_c10faa5a-5c26-49ce-a4e9-0e3ff160f369"}, {"arrows": "to", "color": "#FF5722", "from": "target_16", "to": "route_16_60c78db0-0cff-45cb-8a80-03ba83eb381c"}, {"arrows": "to", "color": "#FF5722", "from": "target_16", "to": "route_16_b67bb74c-6965-4b14-a072-6c084310660e"}, {"arrows": "to", "color": "#9C27B0", "from": "target_17", "to": "route_17_60c78db0-0cff-45cb-8a80-03ba83eb381c"}, {"arrows": "to", "color": "#9C27B0", "from": "target_17", "to": "route_17_4de17c7b-a464-4704-8bb7-f6963773b7c1"}, {"arrows": "to", "color": "#3F51B5", "from": "target_18", "to": "route_18_60c78db0-0cff-45cb-8a80-03ba83eb381c"}, {"arrows": "to", "color": "#3F51B5", "from": "target_18", "to": "route_18_5e899a32-d11c-4b44-b81a-7bfb66624eda"}, {"arrows": "to", "color": "#009688", "from": "target_19", "to": "route_19_60c78db0-0cff-45cb-8a80-03ba83eb381c"}, {"arrows": "to", "color": "#009688", "from": "target_19", "to": "route_19_261ab6a4-d119-4130-a307-86d7fb9e0178"}, {"arrows": "to", "color": "#FF9800", "from": "target_20", "to": "route_20_60c78db0-0cff-45cb-8a80-03ba83eb381c"}, {"arrows": "to", "color": "#FF9800", "from": "target_20", "to": "route_20_eb6ea37c-f339-4037-a6b4-ad675114c66b"}, {"arrows": "to", "color": "#FF5722", "from": "target_21", "to": "route_21_60c78db0-0cff-45cb-8a80-03ba83eb381c"}, {"arrows": "to", "color": "#FF5722", "from": "target_21", "to": "route_21_f1362aaf-e888-4ec5-810d-c5da4483c0a7"}, {"arrows": "to", "color": "#9C27B0", "from": "target_22", "to": "route_22_23711955-48a1-41f7-ab21-c1eaa3b6b307"}, {"arrows": "to", "color": "#9C27B0", "from": "target_22", "to": "route_22_5b7ac276-1222-40f3-850c-84ecefff4018"}, {"arrows": "to", "color": "#9C27B0", "from": "target_22", "to": "route_22_df703e4b-c31b-4beb-b0fd-d6aa4880e88c"}, {"arrows": "to", "color": "#3F51B5", "from": "target_23", "to": "route_23_23711955-48a1-41f7-ab21-c1eaa3b6b307"}, {"arrows": "to", "color": "#3F51B5", "from": "target_23", "to": "route_23_5b7ac276-1222-40f3-850c-84ecefff4018"}, {"arrows": "to", "color": "#3F51B5", "from": "target_23", "to": "route_23_13e11092-04f4-4f6e-ad19-a6aa49d72edb"}, {"arrows": "to", "color": "#009688", "from": "target_24", "to": "route_24_a1e81514-8ccf-4571-92ab-51621aef2237"}, {"arrows": "to", "color": "#009688", "from": "target_24", "to": "route_24_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5"}, {"arrows": "to", "color": "#009688", "from": "target_24", "to": "route_24_5545e7da-5d79-43c4-9609-4ae0d7772e59"}, {"arrows": "to", "color": "#FF9800", "from": "target_25", "to": "route_25_23711955-48a1-41f7-ab21-c1eaa3b6b307"}, {"arrows": "to", "color": "#FF9800", "from": "target_25", "to": "route_25_5b7ac276-1222-40f3-850c-84ecefff4018"}, {"arrows": "to", "color": "#FF9800", "from": "target_25", "to": "route_25_de9dd80b-e0ee-4d26-bae5-618c6ca9f1c9"}, {"arrows": "to", "color": "#FF5722", "from": "target_26", "to": "route_26_23711955-48a1-41f7-ab21-c1eaa3b6b307"}, {"arrows": "to", "color": "#FF5722", "from": "target_26", "to": "route_26_5b7ac276-1222-40f3-850c-84ecefff4018"}, {"arrows": "to", "color": "#FF5722", "from": "target_26", "to": "route_26_3ea996b5-0374-4cfd-87ce-40a6bc580d8c"}, {"arrows": "to", "color": "#9C27B0", "from": "target_27", "to": "route_27_23711955-48a1-41f7-ab21-c1eaa3b6b307"}, {"arrows": "to", "color": "#9C27B0", "from": "target_27", "to": "route_27_5b7ac276-1222-40f3-850c-84ecefff4018"}, {"arrows": "to", "color": "#9C27B0", "from": "target_27", "to": "route_27_3caf3224-61f4-4af0-9cac-fdf1bb234101"}, {"arrows": "to", "color": "#3F51B5", "from": "target_28", "to": "route_28_a1e81514-8ccf-4571-92ab-51621aef2237"}, {"arrows": "to", "color": "#3F51B5", "from": "target_28", "to": "route_28_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5"}, {"arrows": "to", "color": "#3F51B5", "from": "target_28", "to": "route_28_5ee74128-4694-4252-b1ad-25402b002e06"}, {"arrows": "to", "color": "#009688", "from": "target_29", "to": "route_29_23711955-48a1-41f7-ab21-c1eaa3b6b307"}, {"arrows": "to", "color": "#009688", "from": "target_29", "to": "route_29_5b7ac276-1222-40f3-850c-84ecefff4018"}, {"arrows": "to", "color": "#009688", "from": "target_29", "to": "route_29_720d172a-fb08-4f5c-8c69-9a9827388318"}, {"arrows": "to", "color": "#FF9800", "from": "target_30", "to": "route_30_23711955-48a1-41f7-ab21-c1eaa3b6b307"}, {"arrows": "to", "color": "#FF9800", "from": "target_30", "to": "route_30_5b7ac276-1222-40f3-850c-84ecefff4018"}, {"arrows": "to", "color": "#FF9800", "from": "target_30", "to": "route_30_da3d451c-ee85-4c67-8c64-864cf9a6bd6b"}, {"arrows": "to", "color": "#FF5722", "from": "target_31", "to": "route_31_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8"}, {"arrows": "to", "color": "#FF5722", "from": "target_31", "to": "route_31_5c24aefa-e554-4283-99bb-5ef1952e6d52"}, {"arrows": "to", "color": "#9C27B0", "from": "target_32", "to": "route_32_a1e81514-8ccf-4571-92ab-51621aef2237"}, {"arrows": "to", "color": "#9C27B0", "from": "target_32", "to": "route_32_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5"}, {"arrows": "to", "color": "#9C27B0", "from": "target_32", "to": "route_32_d7ad5f08-43f1-4124-9b11-a09ddfbe0291"}, {"arrows": "to", "color": "#3F51B5", "from": "target_33", "to": "route_33_23711955-48a1-41f7-ab21-c1eaa3b6b307"}, {"arrows": "to", "color": "#3F51B5", "from": "target_33", "to": "route_33_5b7ac276-1222-40f3-850c-84ecefff4018"}, {"arrows": "to", "color": "#3F51B5", "from": "target_33", "to": "route_33_a774b7b9-6ffa-4670-b82b-0d6d8864cc7b"}, {"arrows": "to", "color": "#009688", "from": "target_34", "to": "route_34_a1e81514-8ccf-4571-92ab-51621aef2237"}, {"arrows": "to", "color": "#009688", "from": "target_34", "to": "route_34_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5"}, {"arrows": "to", "color": "#009688", "from": "target_34", "to": "route_34_3a014dd3-430c-4e34-ba14-c98f755ced31"}, {"arrows": "to", "color": "#FF9800", "from": "target_35", "to": "route_35_a1e81514-8ccf-4571-92ab-51621aef2237"}, {"arrows": "to", "color": "#FF9800", "from": "target_35", "to": "route_35_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5"}, {"arrows": "to", "color": "#FF9800", "from": "target_35", "to": "route_35_a2b8cc85-8024-4b0e-b7ef-7625767d2e9e"}, {"arrows": "to", "color": "#FF5722", "from": "target_36", "to": "route_36_a1e81514-8ccf-4571-92ab-51621aef2237"}, {"arrows": "to", "color": "#FF5722", "from": "target_36", "to": "route_36_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5"}, {"arrows": "to", "color": "#FF5722", "from": "target_36", "to": "route_36_c607e3fe-0dc9-4f2a-bb91-0211b1bf2d2f"}, {"arrows": "to", "color": "#9C27B0", "from": "target_37", "to": "route_37_a1e81514-8ccf-4571-92ab-51621aef2237"}, {"arrows": "to", "color": "#9C27B0", "from": "target_37", "to": "route_37_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5"}, {"arrows": "to", "color": "#9C27B0", "from": "target_37", "to": "route_37_e7b91c84-b455-4ff5-a53e-4f9e57da75bc"}, {"arrows": "to", "color": "#3F51B5", "from": "target_38", "to": "route_38_a1e81514-8ccf-4571-92ab-51621aef2237"}, {"arrows": "to", "color": "#3F51B5", "from": "target_38", "to": "route_38_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5"}, {"arrows": "to", "color": "#3F51B5", "from": "target_38", "to": "route_38_3b8f87b4-88b8-432f-952c-3364430d517f"}, {"arrows": "to", "color": "#009688", "from": "target_39", "to": "route_39_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8"}, {"arrows": "to", "color": "#009688", "from": "target_39", "to": "route_39_e17fc251-3511-43ab-8cb2-a2d223920ce8"}, {"arrows": "to", "color": "#FF9800", "from": "target_40", "to": "route_40_a1e81514-8ccf-4571-92ab-51621aef2237"}, {"arrows": "to", "color": "#FF9800", "from": "target_40", "to": "route_40_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5"}, {"arrows": "to", "color": "#FF9800", "from": "target_40", "to": "route_40_f3f70f30-b9a6-4011-b715-8694c4d484ac"}, {"arrows": "to", "color": "#FF5722", "from": "target_41", "to": "route_41_a1e81514-8ccf-4571-92ab-51621aef2237"}, {"arrows": "to", "color": "#FF5722", "from": "target_41", "to": "route_41_7c27f7f1-d99d-480c-9d97-3da3ca3eb7e5"}, {"arrows": "to", "color": "#FF5722", "from": "target_41", "to": "route_41_e0bec78f-4c17-41c4-b040-1b3ac9c7e67e"}, {"arrows": "to", "color": "#9C27B0", "from": "target_42", "to": "route_42_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8"}, {"arrows": "to", "color": "#9C27B0", "from": "target_42", "to": "route_42_41d6b800-6b3d-47ba-bc21-1ad22f975fdb"}, {"arrows": "to", "color": "#3F51B5", "from": "target_43", "to": "route_43_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8"}, {"arrows": "to", "color": "#3F51B5", "from": "target_43", "to": "route_43_f8a64c3f-afd2-4b65-9124-b4d196cf88f9"}, {"arrows": "to", "color": "#009688", "from": "target_44", "to": "route_44_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8"}, {"arrows": "to", "color": "#009688", "from": "target_44", "to": "route_44_36b23191-3d86-4565-9a3a-91b723410957"}, {"arrows": "to", "color": "#FF9800", "from": "target_45", "to": "route_45_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8"}, {"arrows": "to", "color": "#FF9800", "from": "target_45", "to": "route_45_1e2d54e9-6500-4554-b22d-ed26f488f75a"}, {"arrows": "to", "color": "#FF5722", "from": "target_46", "to": "route_46_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8"}, {"arrows": "to", "color": "#FF5722", "from": "target_46", "to": "route_46_de869558-a975-4749-a3ad-0deaef5d0b33"}, {"arrows": "to", "color": "#9C27B0", "from": "target_47", "to": "route_47_818a60ca-dcb4-4ae0-a2b7-2ccfc8ab2ea8"}, {"arrows": "to", "color": "#9C27B0", "from": "target_47", "to": "route_47_8e0a74b3-bd5c-47e2-aee1-47cfc48e5369"}, {"arrows": "to", "color": "#3F51B5", "from": "target_48", "to": "route_48_0bcc5f2b-cdfc-48fc-8d17-9a5cbd6f595f"}, {"arrows": "to", "color": "#3F51B5", "from": "target_48", "to": "route_48_38abce4b-16c4-4dfa-8762-8f6015abd4a2"}, {"arrows": "to", "color": "#009688", "from": "target_49", "to": "route_49_0a7f2ce5-c694-4f41-8aec-f31ce1744f32"}, {"arrows": "to", "color": "#009688", "from": "target_49", "to": "route_49_c25049e8-e1a3-4e9a-9423-cb48ec9a6c69"}, {"arrows": "to", "color": "#FF9800", "from": "target_50", "to": "route_50_0a7f2ce5-c694-4f41-8aec-f31ce1744f32"}, {"arrows": "to", "color": "#FF9800", "from": "target_50", "to": "route_50_6ed4f57c-6442-45e4-b1f0-d30b23795ebc"}, {"arrows": "to", "color": "#FF5722", "from": "target_51", "to": "route_51_0bcc5f2b-cdfc-48fc-8d17-9a5cbd6f595f"}, {"arrows": "to", "color": "#FF5722", "from": "target_51", "to": "route_51_922cc712-76cb-4e43-9685-b53cf1d3e44e"}, {"arrows": "to", "color": "#9C27B0", "from": "target_52", "to": "route_52_0a7f2ce5-c694-4f41-8aec-f31ce1744f32"}, {"arrows": "to", "color": "#9C27B0", "from": "target_52", "to": "route_52_0dd8141a-b323-47d6-8756-fb2d51f043fb"}, {"arrows": "to", "color": "#3F51B5", "from": "target_53", "to": "route_53_f6fa3cd5-de1e-4999-bf37-81a3ebd1f7ac"}, {"arrows": "to", "color": "#3F51B5", "from": "target_53", "to": "route_53_e3cdc0b2-39f0-4d45-ac33-34858cfb62fc"}, {"arrows": "to", "color": "#009688", "from": "target_54", "to": "route_54_7d470b93-723b-43fa-aab6-da5a3d1b94a3"}, {"arrows": "to", "color": "#009688", "from": "target_54", "to": "route_54_45282b60-5206-47c6-934a-9d5fdf38edbe"}, {"arrows": "to", "color": "#FF9800", "from": "target_55", "to": "route_55_f6fa3cd5-de1e-4999-bf37-81a3ebd1f7ac"}, {"arrows": "to", "color": "#FF9800", "from": "target_55", "to": "route_55_0d03470c-a8f4-4a26-8014-4f8fa0c0ec7d"}, {"arrows": "to", "color": "#FF5722", "from": "target_56", "to": "route_56_7d470b93-723b-43fa-aab6-da5a3d1b94a3"}, {"arrows": "to", "color": "#FF5722", "from": "target_56", "to": "route_56_d48c4cbc-0df6-41da-82ef-c08c5d0ed782"}, {"arrows": "to", "color": "#9C27B0", "from": "target_57", "to": "route_57_5540a168-a17e-4ce3-909e-def1dae804da"}, {"arrows": "to", "color": "#9C27B0", "from": "target_57", "to": "route_57_dc1f62d7-d183-4b20-b544-12463667b307"}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": true, "hierarchicalRepulsion": {"centralGravity": 0.3, "springLength": 100, "springConstant": 0.01, "nodeDistance": 120, "damping": 0.09}, "maxVelocity": 50, "solver": "hierarchicalRepulsion", "timestep": 0.35, "stabilization": {"iterations": 150}}, "layout": {"hierarchical": {"enabled": true, "levelSeparation": 150, "nodeSpacing": 100, "treeSpacing": 200, "blockShifting": true, "edgeMinimization": true, "parentCentralization": true, "direction": "UD", "sortMethod": "directed"}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  
                      network.on("stabilizationProgress", function(params) {
                          document.getElementById('loadingBar').removeAttribute("style");
                          var maxWidth = 496;
                          var minWidth = 20;
                          var widthFactor = params.iterations/params.total;
                          var width = Math.max(minWidth,maxWidth * widthFactor);
                          document.getElementById('bar').style.width = width + 'px';
                          document.getElementById('text').innerHTML = Math.round(widthFactor*100) + '%';
                      });
                      network.once("stabilizationIterationsDone", function() {
                          document.getElementById('text').innerHTML = '100%';
                          document.getElementById('bar').style.width = '496px';
                          document.getElementById('loadingBar').style.opacity = 0;
                          // really clean the dom element
                          setTimeout(function () {document.getElementById('loadingBar').style.display = 'none';}, 500);
                      });
                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>