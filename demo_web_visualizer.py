#!/usr/bin/env python3
"""
Demo script for the interactive web visualizer.
"""

import json
from web_visualizer import InteractiveSynthesisVisualizer

def create_demo_visualization():
    """Create a demo visualization using sample data."""
    
    # Your actual data structure
    sample_data = {
        "_id": "bb564c09-e620-48b4-b462-6df77c286c8b",
        "route_id": 1,
        "target_smiles": "Clc1cccnc1Cl",
        "num_steps": 2,
        "total_route_score": 0.502830946,
        "data": [
            {
                "step": 1,
                "parent_molecule_id": "2e4d97d2-ac12-40ac-85bb-5693328f3d1a",
                "parent_reaction_id": None,
                "reaction_id": "beb42a86-28d4-4adf-8cf0-792b0450ff0e",
                "reaction_string": "ClCl.Clc1ccccn1>ClC(Cl)Cl>Clc1cccnc1Cl",
                "retro_smiles": "ClCl.Clc1ccccn1",
                "reagents": "ClC(Cl)Cl",
                "score": 0.49745951149999995,
                "rxn_class": {
                    "reaction_name": "Chlorination",
                    "reaction_classname": "Halogenation"
                },
                "reactants": [
                    {
                        "smiles": "ClCl",
                        "name": "Chlorine",
                        "synthesis_score": 1.2253340421847752,
                        "is_terminal": "TERMINAL"
                    },
                    {
                        "smiles": "Clc1ccccn1",
                        "name": "2-chloropyridine",
                        "synthesis_score": 1.2376308399757947,
                        "is_terminal": "UNSOLVED"
                    }
                ]
            },
            {
                "step": 2,
                "parent_molecule_id": "9e173b91-6024-4812-92cb-fd212cc8644a",
                "parent_reaction_id": "beb42a86-28d4-4adf-8cf0-792b0450ff0e",
                "reaction_id": "4ea103d8-c978-423d-a5e2-c1076f8a6eb7",
                "reaction_string": "Clc1cccc(Cl)n1>C1CCOC1.[Al+3].[H-].[H-].[H-].[H-].[Li+]>Clc1ccccn1",
                "retro_smiles": "Clc1cccc(Cl)n1",
                "reagents": "C1CCOC1.[Al+3].[H-].[H-].[H-].[H-].[Li+]",
                "score": 0.5082023805,
                "rxn_class": {
                    "reaction_name": "Dechlorination",
                    "reaction_classname": "Other functional group interconversion"
                },
                "reactants": [
                    {
                        "smiles": "Clc1cccc(Cl)n1",
                        "name": "2,6-dichloropyridine",
                        "synthesis_score": 1.2276062370620435,
                        "is_terminal": "TERMINAL"
                    }
                ]
            }
        ]
    }
    
    # Create the interactive visualization
    visualizer = InteractiveSynthesisVisualizer()
    result = visualizer.visualize_from_db_data(sample_data, "demo_synthesis_tree.html")
    
    if result:
        print("🎉 Demo visualization created!")
        print(f"📂 File: {result}")
        print("🌐 Open this file in your web browser to see the interactive visualization")
        print("\n✨ Features:")
        print("  • 🖱️  Click and drag to pan")
        print("  • 🔍 Mouse wheel to zoom")
        print("  • 🎯 Hover over nodes for detailed information")
        print("  • 🎨 Color-coded nodes and relationships")
        print("  • 📊 Hierarchical layout with physics simulation")
        
        # Create instructions HTML
        create_instructions_html()
        
    return result

def create_instructions_html():
    """Create an instructions HTML file."""
    instructions_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Synthesis Tree Visualizer - Instructions</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
            .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
            .feature { margin: 10px 0; padding: 10px; background: #e8f5e8; border-radius: 3px; }
            .color-legend { display: flex; flex-wrap: wrap; gap: 15px; margin: 20px 0; }
            .color-item { display: flex; align-items: center; }
            .color-box { width: 20px; height: 20px; margin-right: 8px; border-radius: 3px; }
            .usage { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🧪 Interactive Synthesis Tree Visualizer</h1>
            <p>Visualize retro synthesis routes with interactive, zoomable, and hoverable diagrams.</p>
        </div>
        
        <h2>🎨 Node Color Legend</h2>
        <div class="color-legend">
            <div class="color-item">
                <div class="color-box" style="background-color: #FF9800;"></div>
                <span><strong>Target Molecule</strong> - The synthesis goal</span>
            </div>
            <div class="color-item">
                <div class="color-box" style="background-color: #2196F3;"></div>
                <span><strong>Intermediate Molecule</strong> - Molecules produced during synthesis</span>
            </div>
            <div class="color-item">
                <div class="color-box" style="background-color: #FFC107;"></div>
                <span><strong>Reaction</strong> - Chemical transformation steps</span>
            </div>
            <div class="color-item">
                <div class="color-box" style="background-color: #4CAF50;"></div>
                <span><strong>Terminal Molecule</strong> - Starting materials/building blocks</span>
            </div>
        </div>
        
        <h2>🔗 Edge Types</h2>
        <div class="feature">
            <strong>🔵 Blue Solid Lines:</strong> Tree parent relationships (molecule → reaction)
        </div>
        <div class="feature">
            <strong>🔴 Red Dashed Lines:</strong> Route dependencies (reaction → reaction)
        </div>
        <div class="feature">
            <strong>⚫ Gray Lines:</strong> Reaction produces reactant
        </div>
        
        <h2>🎮 Interactive Features</h2>
        <div class="feature">
            <strong>🖱️ Pan:</strong> Click and drag to move around the diagram
        </div>
        <div class="feature">
            <strong>🔍 Zoom:</strong> Use mouse wheel to zoom in/out
        </div>
        <div class="feature">
            <strong>ℹ️ Details:</strong> Hover over any node to see detailed information
        </div>
        <div class="feature">
            <strong>🎯 Focus:</strong> Click on nodes to highlight connections
        </div>
        
        <div class="usage">
            <h3>📋 Usage Examples</h3>
            <pre>
# Install dependencies
pip install pyvis networkx pymongo

# Visualize from MongoDB
python web_visualizer.py --request-id "your-request-id"

# Visualize from JSON file  
python web_visualizer.py --json-file route_data.json

# Custom output file
python web_visualizer.py --request-id "your-id" --output my_tree.html
            </pre>
        </div>
        
        <h2>🔍 What This Helps You Verify</h2>
        <ul>
            <li><strong>Parent Relationships:</strong> Verify parent_molecule_id mappings</li>
            <li><strong>Route Dependencies:</strong> Check parent_reaction_id connections</li>
            <li><strong>Synthesis Flow:</strong> Understand the overall reaction sequence</li>
            <li><strong>Terminal Nodes:</strong> Identify starting materials vs intermediates</li>
        </ul>
        
        <p><em>🎉 Happy visualizing! This interactive tool makes it easy to explore and verify your synthesis tree data.</em></p>
    </body>
    </html>
    """
    
    with open("visualizer_instructions.html", "w") as f:
        f.write(instructions_html)
    
    print("📋 Instructions saved as visualizer_instructions.html")

if __name__ == "__main__":
    print("🚀 Creating demo interactive visualization...")
    create_demo_visualization()
    print("\n💡 Next steps:")
    print("1. Install dependencies: pip install pyvis networkx pymongo")
    print("2. Open demo_synthesis_tree.html in your browser")
    print("3. Open visualizer_instructions.html for detailed usage guide")
    print("4. Use web_visualizer.py with your actual MongoDB data!")

