# retro_synthesis_mcts/api/retro_api.py
import pandas as pd
import numpy as np
import joblib
import pickle
import xgboost as xgb
from sklearn.preprocessing import StandardScaler
import uuid
import subprocess

from api.reaction_classification_api import *
from api.trasformers_api import *
from core.single_step_retro.disconnections_relaxed import *
from infra.db.disconnections_db import *
from infra.db.disconnections_fileStorage import *
from utils.ltr_features import *
from utils.remove_hallucinations import *
import requests

class SingleStepRetroRelaxedAPI:
    """API client for single-step retrosynthesis predictions."""
    
    def __init__(self,transformer_AutoTag,transformer_T1,transformer_T2, transformer_T3):
        """
        Initialize the retrosynthesis API.
        """    
        list_substructures_path= 'assets/stocks/list_conditionnal_substructures_tags_R2.csv'
        if list_substructures_path != '':
            with open(list_substructures_path, 'r') as f:
                reader = csv.reader(f)
                substructure_list = list(reader)   

        self.disconnection_tool = TopDisconnectionsRelaxed(
            transformer_AutoTag,
            transformer_T1,
            transformer_T2, 
            transformer_T3,                   
            uspto_t1_path="assets/mttl_models/USPTO_STEREO_separated_T1_Retro_255000.pt",
            uspto_t2_path="assets/mttl_models/USPTO_STEREO_separated_T2_Reagent_Pred_225000.pt",
            uspto_t3_path="assets/mttl_models/USPTO_STEREO_separated_T3_Forward_255000.pt",
            autotag_model_path="assets/mttl_models/USPTO_STEREO_separated_T0_AutoTag_260000.pt",
            substructure_list=substructure_list # Example substructures
        )  
        ltr_model_path = "assets/ltr/minimal_ltr_1_8_3.joblib"
        components = joblib.load(ltr_model_path)
        self.ltr_model = components['model']
        self.ltr_scaler = components['scaler']
        self.feature_columns = components['feature_columns']
    
    def get_retrosynthesis_reactions(self, target_smiles):
        """
        Get possible retrosynthesis reactions for a target molecule.
        
        Args:
            target_smiles (str): SMILES of the target molecule
            
        Returns:
            list: List of Reaction objects
        """
        reactions = []
        target_predictions = pd.DataFrame() 

        storage = DisconnectionsStorage()
        target_predictions = storage.get_data_from_blob(self.canonicalize_smiles(target_smiles), is_disconnections=False) 
        if len(target_predictions) >0:
            logger.debug(f"Loaded {len(target_predictions)} processed LTR scores from fileStorage")
            return target_predictions         

        check_disconnections_fileStorage = 1
        if check_disconnections_fileStorage ==1:
            target_predictions = storage.get_data_from_blob(self.canonicalize_smiles(target_smiles)) 
            if 'index' in target_predictions.columns:
                target_predictions['index'] = target_predictions.index
            print(f"Loaded {len(target_predictions)} records from fileStorage")  
            if len(target_predictions) >0:
                print(target_predictions.shape)
                #print(target_predictions.iloc[0])
                target_predictions= target_predictions[['T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf', 'retro_conf_RT', 'retro_conf_AT', 'retro_conf_ST', 'retro_conf_askcos_AT', 'retro_conf_askcos_TR', 'retro_conf_askcos_EM']]
                target_predictions['index'] = target_predictions.index
                #target_predictions.columns=['index', 'T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf','Forward_Model', 'Reagents', 'Forward_Prediction', 'Prob_Forward_Prediction_1','Prob_Forward_Prediction_2']#, 'rxn_string', 'rxn_class']
        
        #exit(1)
        if len(target_predictions) ==0 :
            print("No disconnections found in DB - creating from scratch")
            # Filter predictions for the target molecule
            target_predictions = self.disconnection_tool.get_potential_disconnections(target_smiles)    
            if target_predictions.empty:
                return reactions
            print("*********")
            print("+++++++")
            print(f"Created {target_predictions.shape} disconnections from scratch")
            print(target_predictions.iloc[0]) 
            #exit(1)        

            insert_disconnections_fileStorage = 1
            if insert_disconnections_fileStorage ==1:  
                storage = DisconnectionsStorage()
                target_predictions_temp= target_predictions[['T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf', 'retro_conf_RT', 'retro_conf_AT', 'retro_conf_ST', 'retro_conf_askcos_AT', 'retro_conf_askcos_TR', 'retro_conf_askcos_EM']]
                    
                success = storage.store_data_to_blob(target_predictions_temp, self.canonicalize_smiles(target_smiles), analysis_id='test_analysis', is_disconnections=True)
                print(f"Save successful: {success}")
                #exit(1)
                if success:
                    print("Data inserted successfully!")
                else:
                    print("Failed to insert data")                      
    
        else:
            print(f"Found {len(target_predictions)} disconnections in DB: returning")
        
        # 1. Cut off based RetroConf Score
        # Rank-based percentile approach:
        # Top 20% (>= 0.005323): 3042 rows
        # Top 10% (>= 0.022504): 1521 rows
        # Top 5% (>= 0.078865): 761 rows
        # Top 1% (>= 0.512325): 153 rows
        print(target_predictions.shape)
        if target_predictions.shape[0]:
            target_predictions = target_predictions[target_predictions['Retro_Conf'] > 0.005]
        
            target_predictions.drop_duplicates(subset=['Retro'], keep='first', inplace=True)
            target_predictions = target_predictions[target_predictions['Target'] != target_predictions['Retro']]
        
            print(target_predictions.head())

            ##### OLD LTR SECTION STARTS
            #exit(1)
            # target_predictions = enhance_dataframe_with_scores(target_predictions)
            # print(target_predictions.columns)
            # #exit(1)
            # target_predictions = target_predictions[target_predictions['num_reactants'] <= 3]
            # target_predictions = create_derived_features(target_predictions)
            # print(target_predictions.columns)
            # # Scale the features and get score
            # # Ensure all feature columns exist
            # for col in self.feature_columns:
            #     if col not in target_predictions.columns:
            #         target_predictions[col] = 0
            
            # # Extract features
            # X = target_predictions[self.feature_columns].values
            # # Scale features
            # X_scaled = self.ltr_scaler.transform(X)
            # # Make predictions
            # predictions = self.ltr_model.predict(X_scaled)
            # # Add to df
            # target_predictions['ltr_score'] = predictions
            # print(target_predictions.shape)
            # #exit(1)
            # target_predictions['ltr_score_scaled'] = (target_predictions['ltr_score'] - (-10.0)) / (10.0 - (-10.0))

            ### OLD LTR SECTION ENDS

            ltr_filename = str(uuid.uuid4())
            # local config
            input_ltr_path = f"/opt/ltr_runner/inputs/{ltr_filename}.csv"
            output_ltr_path = f"/opt/ltr_runner/outputs/{ltr_filename}.csv"
            #prod config
            # input_ltr_path = f"./ltr_runner/inputs/{ltr_filename}.csv"
            # output_ltr_path = f"./ltr_runner/outputs/{ltr_filename}.csv"
            # Extract directory paths
            input_dir = os.path.dirname(input_ltr_path)
            output_dir = os.path.dirname(output_ltr_path)

            # Create directories if they don't exist
            os.makedirs(input_dir, exist_ok=True)
            os.makedirs(output_dir, exist_ok=True)

            logger.info(input_ltr_path)
            target_predictions.to_csv(input_ltr_path, index=False)

            python_path = os.environ.get("RETRO_PYTHON", "/opt/conda/envs/ltr_runner_env/bin/python")

            # local path change for
            cmd = [
                python_path,
                "/opt/ltr_runner/run_ltr.py",
                "--input", input_ltr_path,
                "--output", output_ltr_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            target_predictions = pd.read_csv(output_ltr_path)
            logger.info(target_predictions.head())

            min_val = target_predictions['ltr_score'].min()
            max_val = target_predictions['ltr_score'].max()
            
            target_predictions['ltr_score_scaled'] = (target_predictions['ltr_score'] - min_val) / (max_val - min_val)
            
            ### T2
            # 2. Call T2
            # target_predictions = self.disconnection_tool.retro_model.call_T2(target_predictions)
            # logger.info(target_predictions.head())
            # logger.info(target_predictions.columns)
            # target_predictions['Reagents'] = target_predictions['Reagents'].apply(clean_reagents_pandas)
            #### T2 edns

            ### Parrot cmd python starts here
            # target_predictions['rxn_smiles'] = target_predictions.apply(lambda s: s['Retro'] + '>>' + target_smiles, axis=1)
            
            # parrot_filename = str(uuid.uuid4())
            # input_parrot_path = f"/opt/parrot/inputs/{parrot_filename}.csv"
            # output_parrot_path = f"/opt/parrot/outputs/{parrot_filename}.csv"
            # # Extract directory paths
            # input_dir = os.path.dirname(input_parrot_path)
            # output_dir = os.path.dirname(output_parrot_path)

            # # Create directories if they don't exist
            # os.makedirs(input_dir, exist_ok=True)
            # os.makedirs(output_dir, exist_ok=True)

            # logger.info(input_parrot_path)
            # target_predictions.to_csv(input_parrot_path, index=False)

            # parrot_python_path = os.environ.get("PARROT_PYTHON", "/opt/conda/envs/parrot_env/bin/python")

            # cmd = [
            #     parrot_python_path,
            #     "run_parrot_t2.py",
            #     "--input", input_parrot_path,
            #     "--output", output_parrot_path
            # ]

            # result = subprocess.run(cmd, capture_output=True, text=True, cwd="/opt/Parrot")
            # parrot_result = pd.read_csv(output_parrot_path)

            ### Parrot cmd python ends here

            #### Parrot API calls start here
            target_predictions['rxn_smiles'] = target_predictions.apply(lambda s: s['Retro'] + '>>' + target_smiles, axis=1)

            data = {
                "rxn_smiles": target_predictions['rxn_smiles'].unique().tolist()
            }
            
            parrot_response = requests.post("http://localhost:5050/get_reagents", json=data)
            parrot_response = parrot_response.json()
            
            # Convert to DataFrame
            parrot_result = pd.DataFrame(parrot_response['results'])[['rxn_string', 'score', 'reagents']].rename(columns={'rxn_string': 'rxn_smiles', 'score': 'parrot_score', 'reagents': 'Reagents'})

            target_predictions = target_predictions.merge(parrot_result, on='rxn_smiles', how='left')
            logger.info(target_predictions.head())
            logger.info(target_predictions.shape)

            #### Parrot API calls ends here


            #exit(1)
            #remove invalid discconections by roundtrip logid
            # Keep predictions where T3 predicts the correct target, and target is not in the retro prediction:
            # print(target_predictions.shape)
            # target_predictions_Forw_val = target_predictions[target_predictions['Target'] == target_predictions['Forward_Prediction']]
            # print(target_predictions_Forw_val.shape)
            
            # print(target_predictions_Forw_val.shape)        

            target_predictions['rxn_string'] =target_predictions.apply(lambda s: s['Retro'] + '>' + s['Reagents'] + '>' + target_smiles,axis=1)
            target_predictions['rxn_class'] = target_predictions.apply(lambda s: get_reaction_class(s['rxn_string']),axis=1)
            
            # Sort by prediction_certainty from rxn_class (descending order to keep highest certainty first)
            target_predictions = target_predictions.sort_values( by='rxn_class', key=lambda x: x.apply(lambda rxn_class: rxn_class.get('prediction_certainty', 0.0) if isinstance(rxn_class, dict) else 0.0), ascending=False )
            # Nowdrop duplicates (keeping the highest prediction_certainty for each Retro)
            target_predictions.drop_duplicates(subset=['Retro'], keep='first', inplace=True)

            
            if target_predictions.shape[0]: 
                success = storage.store_data_to_blob(df=target_predictions, target_smiles=self.canonicalize_smiles(target_smiles), is_disconnections=False)
                #exit(1)
                if success:
                    logger.info(f"Data inserted successfully!")
                else:
                    logger.error(f"Failed to insert data")     

        #exit(1)
        return target_predictions

    def canonicalize_smiles(self, smiles: str) -> str:
        '''
        Molecule canonicalization that does not change the SMILES order of molecules in case of multiple molecules.
        Also neutralizes any charge of the molecules.
        
        Args:
            smiles (str): SMILES string of the molecule(s).
        
        Returns:
            str: Canonicalized SMILES string of the molecule(s).
        '''
        returned = []
        any_error = False
        for molecule in smiles.split('.'):
            molecule = self.neutralize_smi(molecule)
            mol = Chem.MolFromSmiles(molecule)
            if mol is not None:
                returned.append(Chem.MolToSmiles(mol, isomericSmiles=True, canonical=True))
            else: 
                any_error = True
        if not any_error:
            return '.'.join(returned)
        else:
            return ''
        
    def neutralize_smi(self, smiles: str) -> str:        # from: https://www.rdkit.org/docs/Cookbook.html#neutralizing-molecules
        if '-' in smiles or '+' in smiles:
            try:
                mol = Chem.MolFromSmiles(smiles)
                pattern = Chem.MolFromSmarts("[+1!h0!$([*]~[-1,-2,-3,-4]),-1!$([*]~[+1,+2,+3,+4])]")
                at_matches = mol.GetSubstructMatches(pattern)
                at_matches_list = [y[0] for y in at_matches]
                if len(at_matches_list) > 0:
                    for at_idx in at_matches_list:
                        atom = mol.GetAtomWithIdx(at_idx)
                        chg = atom.GetFormalCharge()
                        hcount = atom.GetTotalNumHs()
                        atom.SetFormalCharge(0)
                        atom.SetNumExplicitHs(hcount - chg)
                        atom.UpdatePropertyCache()
                return Chem.MolToSmiles(mol)
            except:
                return smiles
        else:
            return smiles
