#!/usr/bin/env python3
"""
Test the connection logic without requiring external libraries.
"""

def test_connection_logic():
    """Test the connection logic with sample data."""
    
    sample_data = {
        "route_id": 1,
        "target_smiles": "Clc1cccnc1Cl",
        "data": [
            {
                "step": 1,
                "parent_molecule_id": "2e4d97d2-ac12-40ac-85bb-5693328f3d1a",
                "parent_reaction_id": None,
                "reaction_id": "beb42a86-28d4-4adf-8cf0-792b0450ff0e",
                "reaction_string": "ClCl.Clc1ccccn1>ClC(Cl)Cl>Clc1cccnc1Cl",
                "reactants": [
                    {"smiles": "ClCl", "is_terminal": "TERMINAL"},
                    {"smiles": "Clc1ccccn1", "is_terminal": "UNSOLVED"}
                ]
            },
            {
                "step": 2,
                "parent_molecule_id": "9e173b91-6024-4812-92cb-fd212cc8644a",
                "parent_reaction_id": "beb42a86-28d4-4adf-8cf0-792b0450ff0e",
                "reaction_string": "Clc1cccc(Cl)n1>C1CCOC1.[Al+3].[H-].[H-].[H-].[H-].[Li+]>Clc1ccccn1",
                "reactants": [
                    {"smiles": "Clc1cccc(Cl)n1", "is_terminal": "TERMINAL"}
                ]
            }
        ]
    }
    
    print("🧪 TESTING CONNECTION LOGIC")
    print("=" * 40)
    
    reactions = sample_data["data"]
    target_smiles = sample_data["target_smiles"]
    
    # Simulate the visualization logic
    nodes = []
    edges = []
    
    # Add target node
    nodes.append(("target", target_smiles, "TARGET"))
    print(f"✅ Added target node: {target_smiles}")
    
    # Add reaction nodes
    for reaction in reactions:
        step = reaction["step"]
        reaction_id = reaction["reaction_id"]
        reaction_string = reaction["reaction_string"]
        
        nodes.append((reaction_id, f"Step {step}", "REACTION"))
        print(f"✅ Added reaction node: Step {step} ({reaction_id[:8]}...)")
    
    # Connect target to first reaction
    first_reaction = min(reactions, key=lambda x: x["step"])
    edges.append(("target", first_reaction["reaction_id"], "produces"))
    print(f"✅ Connected target → Step {first_reaction['step']}")
    
    # Add route dependencies
    for reaction in reactions:
        parent_rxn_id = reaction.get("parent_reaction_id")
        if parent_rxn_id:
            edges.append((parent_rxn_id, reaction["reaction_id"], "enables"))
            print(f"✅ Added route dependency: {parent_rxn_id[:8]}... → {reaction['reaction_id'][:8]}...")
    
    # Add reactant nodes and connections
    for reaction in reactions:
        reaction_id = reaction["reaction_id"]
        step = reaction["step"]
        
        for i, reactant in enumerate(reaction["reactants"]):
            reactant_smiles = reactant["smiles"]
            is_terminal = reactant["is_terminal"]
            reactant_id = f"{reaction_id}_reactant_{i}"
            
            node_type = "TERMINAL" if is_terminal == "TERMINAL" else "REACTANT"
            nodes.append((reactant_id, reactant_smiles, node_type))
            print(f"✅ Added reactant node: {reactant_smiles} ({node_type})")
            
            # Connect reaction to reactant
            edges.append((reaction_id, reactant_id, "needs"))
            print(f"  └─ Step {step} needs {reactant_smiles}")
            
            # Check if this reactant is produced by another reaction
            for other_reaction in reactions:
                if other_reaction["reaction_id"] != reaction_id:
                    other_rxn_string = other_reaction["reaction_string"]
                    if '>' in other_rxn_string:
                        product = other_rxn_string.split('>')[-1].strip()
                        if product == reactant_smiles:
                            edges.append((other_reaction["reaction_id"], reactant_id, "produces"))
                            print(f"  └─ Step {other_reaction['step']} produces {reactant_smiles}")
    
    print(f"\n📊 SUMMARY:")
    print(f"  Nodes: {len(nodes)}")
    print(f"  Edges: {len(edges)}")
    
    print(f"\n🔗 ALL CONNECTIONS:")
    for source, target, label in edges:
        source_short = source[:8] + "..." if len(source) > 8 else source
        target_short = target[:8] + "..." if len(target) > 8 else target
        print(f"  {source_short} --{label}--> {target_short}")
    
    # Verify expected connections
    print(f"\n✅ VERIFICATION:")
    expected_connections = [
        ("target", "produces"),
        ("enables", "route dependency"),
        ("needs", "reactant requirement"),
        ("produces", "synthesis connection")
    ]
    
    for connection_type, description in expected_connections:
        count = sum(1 for _, _, label in edges if connection_type in label)
        print(f"  {description}: {count} connections")
    
    return len(nodes) > 0 and len(edges) > 0

if __name__ == "__main__":
    success = test_connection_logic()
    
    if success:
        print(f"\n🎉 CONNECTION LOGIC TEST PASSED!")
        print(f"The visualization should now show connected nodes.")
        print(f"\n🚀 Next steps:")
        print(f"1. Install dependencies: pip install pyvis networkx")
        print(f"2. Run: python demo_web_visualizer.py")
        print(f"3. Open the generated HTML file in your browser")
    else:
        print(f"\n❌ CONNECTION LOGIC TEST FAILED!")
        print(f"Check the logic above for issues.")
