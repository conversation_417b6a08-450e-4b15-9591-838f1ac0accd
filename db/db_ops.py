
from enum import Enum
import logging
import uuid
import hashlib
from pymongo import MongoClient
from datetime import datetime
import os

MONGO_URL = os.getenv('MONGO_URL', '**************************************')

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
class RetroStatus(Enum):
    """
    Enum to represent the status of the retro synthesis process.
    """
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"

class DbOps:
    """
    A class to handle MongoDB operations for logging retro synthesis requests.
    """
    def __init__(self):
        self.client = MongoClient(MONGO_URL)
        self.db = self.client['retro_synthesis']
        self.requests_collection = self.db['retro_requests']
        # self._create_indexes()

    def _create_indexes(self):
        """
        Create necessary indexes for the collections.
        """
        self.requests_collection.create_index('request_id', unique=True)

    def insert_log(self, request_id, input_type, input_value, status, user_id='', tenant_id='', error_message=None):
        """
        Insert or update a log entry and track status history.
        """
        current_time = datetime.utcnow()
        
        # Prepare the document for new entries
        log_doc = {
            '_id' : str(uuid.uuid4()),
            'request_id': request_id,
            'input_type': input_type,
            'input_value': input_value,
            'status': status.value,
            'user_id': user_id,
            'tenant_id': tenant_id,
            'error_message': error_message,
            'created_at': current_time,
            'updated_at': current_time
        }

        # Update status and updated_at for existing documents, or insert new document
        self.requests_collection.update_one(
            {'request_id': request_id},
            {
            '$set': {
                'status': status.value,
                'updated_at': current_time
            },
            '$setOnInsert': {k: v for k, v in log_doc.items() if k not in ['status', 'updated_at']}
            },
            upsert=True
        )

        logger.info(f"Updated log for request_id: {request_id}, status: {status.value}")
    
    def insert_retro_data(self, request_id, unique_id, route_id, num_steps, total_route_score,
                      target_smiles, total_cost, raw_smiles, route_level_image, route_name, data,level, config):
        """
        Insert retro synthesis data into the database.
        """
        logger.info(f"Inserting retro data for request_id: {request_id}, unique_id: {unique_id}, route_id: {route_id}")
        current_time = datetime.now()
        generated_id = str(uuid.uuid4())
        
        data_doc = {
            '_id': generated_id,
            'request_id': request_id,
            'route_id': route_id,
            'unique_id': unique_id,
            'target_smiles': target_smiles,
            'route_name': route_name,
            'route_reaction_img': route_level_image,
            'raw_reactants': raw_smiles,
            'num_steps': num_steps,
            'total_route_score': total_route_score,
            'total_cost': total_cost,
            'data': data,
            'route_status': 'IN_PROGRESS',
            'config': config,
            "level" : level,
            'created_at': current_time,
            'updated_at': current_time
        }

        try:
            result = self.db['retro_data'].update_one(
                {'unique_id': unique_id},
                {
                    '$set': {'updated_at': current_time},
                    '$setOnInsert': {k: v for k, v in data_doc.items() if k != 'updated_at'}
                },
                upsert=True
            )

            if result.upserted_id:
                logger.info(f"Inserted new retro data for unique_id: {unique_id}")
                return result.upserted_id
            else:
                logger.info(f"Updated existing retro data for unique_id: {unique_id}")
                # Retrieve the _id of the existing document
                existing_doc = self.db['retro_data'].find_one({'unique_id': unique_id}, {'_id': 1})
                if existing_doc:
                    return existing_doc['_id']
                else:
                    logger.error(f"Could not find existing retro data after update for unique_id: {unique_id}")
                    return None
        except Exception as e:
            logger.error(f"Failed to upsert retro data for unique_id {unique_id}: {str(e)}")
            return None

    def insert_config_history(self, request, config, status):
        """
        Insert retro synthesis data into the database.
        """
        logger.info(f"Inserting config history for request_id: {request}")
        current_time = datetime.now()
        
        data_doc = {
            '_id': str(uuid.uuid4()),   
            'request_id': request,
            'config': config,
            'status': status.value,
            'created_at': current_time,
            'updated_at': current_time
        }

        try:
            result = self.db['retro_config_history'].insert_one(data_doc)
            logger.info(f"Inserted config history for request_id: {request}")
            return result.inserted_id
        except Exception as e:
            logger.error(f"Failed to insert config history for request_id {request}: {str(e)}")
            return None
    
    def get2_top_routes(self, request_id, limit=100):
        query = {
            'request_id': request_id,
            'data': {
                '$elemMatch': {
                    'other_information': {}
                }
            }
        }
        projection = {
            '_id': 1,
            'route_id': 1,
            'num_steps': 1,
            'total_route_score': 1,
            'data': 1
        }

        routes = list(self.db['retro_data'].find(query, projection).sort('total_route_score', -1).limit(limit))

        # Optional: narrow to only steps with empty `other_information`
        for route in routes:
            route['data'] = [step for step in route.get('data', []) if step.get('other_information') == {} or step.get('other_information') is None]

        return routes
    
    def get_top_routes(self, limit=100):
        """
        Retrieve the top routes for a given target SMILES.
        
        Parameters:
        ----------
        target_smiles : str
            The target SMILES string to search for.
        limit : int
            The maximum number of routes to return.
        
        Returns:
        -------
        list
            A list of dictionaries containing the top routes.
        
        """
        query = {
             'route_status': 'IN_PROGRESS'
        }
        projection = {
            '_id': 1,
            'route_id': 1,
            'num_steps': 1,
            'total_route_score': 1,
            'data': 1
        }
        routes = list(self.db['retro_data'].find(query).sort([('created_at', -1), ('total_route_score', -1)]))
        return routes
    
    def get_route_by_id(self, route_id):
        """
        Retrieve the top routes for a given target SMILES.
        
        Parameters:
        ----------
        target_smiles : str
            The target SMILES string to search for.
        limit : int
            The maximum number of routes to return.
        
        Returns:
        -------
        list
            A list of dictionaries containing the top routes.
        
        """
        query = {
             'route_status': 'IN_PROGRESS' , '_id' : route_id
        }

        routes = list(self.db['retro_data'].find(query).sort([('created_at', -1), ('total_route_score', -1)]))
        return routes
    
    def fetch_retro_data_by_request_id_and_target_smiles(self, request_id, target_smiles):
        """
        Fetch the latest created retro synthesis data by target SMILES.
        """
        try:
            cursor = (
                self.db["retro_data"]
                .find({"request_id": request_id, "target_smiles": target_smiles})
                .sort("created_at", -1)
            )

            return list(cursor)
        except Exception as e:
            logging.error(
                f"Failed to fetch retro data for target_smiles {target_smiles}: {str(e)}"
            )
            return None

    def fetch_latest_retro_request_id_by_smiles(self, target_smiles):
        """
        Fetch the retro synthesis request by target SMILES.
        """
        try:
            result = self.db["retro_requests"].find_one(
                {"input_value": target_smiles, "status": "COMPLETED"},
                sort=[("updated_at", -1)]  # Sort by updated_at in descending order
            )
            return result["request_id"] if result else None
        except Exception as e:
            logging.error(
                f"Failed to fetch latest retro request for smiles {target_smiles}: {str(e)}"
            )
            return None

    
    def update_retro_data(self, id, data):
        """
        Update retro synthesis data in the database.
        
        Parameters:
        ----------
        id : str
            The request ID for which to update the data.
        data : dict
            The data to update in the database.
        
        """
        current_time = datetime.now()
        
        result = self.db['retro_data'].update_one(
            {'_id': id},
            {'$set': {'data': data, 'updated_at': current_time , 'route_status' : 'COMPLETED'}}
        )

        
        if result.modified_count > 0:
            logger.info(f"Updated retro data for id: {id}")
        else:
            logger.warning(f"No updates made for id: {id}")
    
    def check_request_id_is_processing(self, request_id):
        existing_doc = self.db['retro_requests'].find_one(
            { 'request_id': request_id}
        )
        if existing_doc:
            logger.info(f"Request already processed: {request_id}")
            return True
        return False
    
    def check_config_is_processed(self, request_id, config):
        """
        Check if a config has already been processed for a request,
        ignoring request-specific fields like request_id and smiles.

        Parameters:
        ----------
        request_id : str
            The request ID to check.
        config : dict
            The full config to check.

        Returns:
        -------
        bool
            True if the config has already been processed, False otherwise.
        """

        # Define the fields that determine uniqueness of a config
        # relevant_keys = ["max_depth", "beam_width", "delay", "checkpoint_frequency"]

        # Extract only relevant keys from the input config
        query_config = config

        # Search for any document with matching config and status COMPLETED
        existing_doc = self.db['retro_config_history'].find_one({
            'status': 'COMPLETED',
            **{f"config.{k}": v for k, v in query_config.items()}
        })

        logger.info(f"Existing doc: {existing_doc} for filtered config: {query_config}")
        if existing_doc:
            logger.info(f"Config already processed for request_id: {request_id} with config: {query_config}")
            return True
        return False


    def close(self):
        """
        Close the MongoDB connection.
        
        """
        self.client.close()
