import argparse
import pandas as pd

from parrot_t2 import run_inference_from_list

def main(input_path, output_path):
    # Load input file (supports CSV or JSON)
    if input_path.endswith(".csv"):
        df = pd.read_csv(input_path)
    elif input_path.endswith(".json"):
        df = pd.read_json(input_path)
    else:
        raise ValueError("Unsupported input file format. Use CSV or JSON.")

    input_list = df['rxn_smiles'].unique().tolist()

    # Run embedding + similarity
    results = run_inference_from_list(input_list)
    results["reagents"] = df[["catalyst1", "solvent1", "solvent2", "reagent1", "reagent2"]].apply(lambda row: ".".join(filter(None, row)), axis=1)
    results = results[['rxn_string', 'reagents', 'score']]

    # Save output
    results.to_csv(output_path, index=False)
    print(f"Output saved to {output_path}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run Parrot for Reagent Prediction.")
    parser.add_argument("--input", type=str, required=True, help="Input CSV/JSON file path")
    parser.add_argument("--output", type=str, required=True, help="Output CSV file path")

    args = parser.parse_args()
    main(args.input, args.output)
