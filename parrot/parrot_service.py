import logging
import pandas as pd
from flask import Flask, request, jsonify
import yaml

import torch
from models.parrot_model import ParrotConditionPredictionModel
from preprocess_script.uspto_script.utils import canonicalize_smiles
from models.utils import caonicalize_rxn_smiles, get_output_results, inference_load


# --- 1. Setup Logging and Flask App ---
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")
logger = logging.getLogger(__name__)
app = Flask(__name__)

# --- 2. Create Global Variables to Hold the Model ---
# This ensures the model is loaded only once into memory.
parrot_model = None
model_args = None
inference_args = None

def format_beam_predictions(test_df, beam_output):
    rows = []
    for idx, beams in enumerate(beam_output):
        rxn = test_df.loc[idx, "rxn_string"]
        for rank, (beam_entry, score) in enumerate(beams, start=1):
            row = {
                "rxn_string": rxn,
                "rank": rank,
                "score": round(score, 4),
                "catalyst1": beam_entry[0],
                "solvent1": beam_entry[1],
                "solvent2": beam_entry[2],
                "reagent1": beam_entry[3],
                "reagent2": beam_entry[4],
            }
            rows.append(row)

    df = pd.DataFrame(rows)
    
    # Optional: add joined reagents column
    df["reagents"] = df[["catalyst1", "solvent1", "solvent2", "reagent1", "reagent2"]].apply(
        lambda row: ".".join(filter(None, row)), axis=1
    )
    
    return df

def initialize_model_and_config(config_path: str, gpu: int = 0):
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)

    model_args = config['model_args']
    dataset_args = config['dataset_args']
    inference_args = config['inference_args']

    label_mapping = inference_load(**dataset_args)
    model_args['decoder_args'].update({
        'condition_label_mapping': label_mapping,
        'tgt_vocab_size': len(label_mapping[0]),
        'dataloader_num_workers': 0
    })

    model_args['use_temperature'] = dataset_args.get('use_temperature', False)
    print('Using Temperature:', model_args['use_temperature'])

    model = ParrotConditionPredictionModel(
        "bert",
        model_args['best_model_dir'],
        args=model_args,
        use_cuda=gpu >= 0,
        cuda_device=gpu
    )

    return model, model_args, inference_args


    
def load_model():
    """
    Initializes the Parrot model and configuration and stores them in global variables.
    This function is called only once when the server starts.
    """
    global parrot_model, model_args, inference_args
    if parrot_model is not None:
        logger.info("Parrot model is already loaded.")
        return

    logger.info("Loading and initializing Parrot model for the first time...")
    try:
        # We call your initialization function but store its output globally
        # Use gpu=-1 to default to CPU if no CUDA is available.
        # Change to gpu=0 if you are certain a GPU is present.
        initialized_model, m_args, i_args = initialize_model_and_config(
            'configs/config_inference_use_uspto.yaml', 
            gpu=-1 
        )
        
        # Store the initialized components in our global variables
        parrot_model = initialized_model
        model_args = m_args
        inference_args = i_args
        logger.info("Parrot model has been successfully loaded into memory.")

    except Exception as e:
        logger.error(f"FATAL: Failed to load Parrot model on startup: {e}", exc_info=True)
        # The app will be unhealthy if the model fails to load.
        parrot_model = None


@app.route('/get_reagents', methods=['POST'])
def get_reagents():
    """
    Prediction endpoint that uses the globally pre-loaded model.
    """
    # First, check if the model was loaded successfully on startup
    if parrot_model is None:
        logger.error("Prediction request failed because the model is not loaded.")
        return jsonify({"error": "Model is not initialized. Check server logs."}), 503 # Service Unavailable

    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON payload"}), 400

        input_rxns = data.get('rxn_smiles', [])
        n_best = data.get('n_best', 1)

        if not isinstance(input_rxns, list):
            return jsonify({"error": "'rxn_smiles' must be a list of strings"}), 400
        
        if not input_rxns:
            return jsonify({"results": []}), 200

        logger.info(f"Received prediction request for {len(input_rxns)} SMILES.")
        
        # --- This logic is adapted from your `run_inference_from_list` function ---

        # 1. Prepare input DataFrame for the model
        test_df = pd.DataFrame({
            'rxn_smiles': input_rxns, # Use 'rxn_smiles' to match the final merge
            'text': input_rxns,
            'labels': [[0] * (8 if model_args['use_temperature'] else 7)] * len(input_rxns)
        })

        # 2. Canonicalize reaction SMILES
        test_df['text'] = test_df.text.apply(caonicalize_rxn_smiles)
        test_df = test_df[test_df['text'] != ''].reset_index(drop=True)

        if test_df.empty:
            logger.warning("No valid SMILES left after canonicalization.")
            return jsonify({"results": []}), 200

        # 3. Run inference using the pre-loaded global model
        beam = inference_args['beam']
        pred_conditions, _ = parrot_model.condition_beam_search(
            test_df,
            output_dir=model_args['best_model_dir'],
            beam=beam,
            test_batch_size=8,
            n_best=n_best,
            calculate_topk_accuracy=False
        )

        # 4. Format the predictions into a clean DataFrame
        # NOTE: Your original format_beam_predictions had a bug looking for "rxn_string". 
        # I have corrected the logic to use "rxn_smiles" consistently.
        test_df.rename(columns={'rxn_smiles': 'rxn_string'}, inplace=True) # Temporarily rename for the function
        results_df = format_beam_predictions(test_df, pred_conditions)
        
        # Return the results as a JSON list of records
        return jsonify({"results": results_df.to_dict(orient='records')}), 200

    except Exception as e:
        logger.error(f"An error occurred during prediction: {e}", exc_info=True)
        return jsonify({"error": "An internal server error occurred during prediction."}), 500


@app.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint that verifies if the model is loaded.
    """
    if parrot_model is not None:
        return jsonify({"status": "OK", "message": "Model is loaded and ready."}), 200
    else:
        return jsonify({"status": "UNHEALTHY", "message": "Model failed to load."}), 503


# --- 3. Load the model when the script is first run by the server ---
load_model()


if __name__ == '__main__':
    # This block is for local development testing ONLY.
    # In production, you MUST use a proper WSGI server like Gunicorn.
    app.run(host='0.0.0.0', port=5050, debug=False)