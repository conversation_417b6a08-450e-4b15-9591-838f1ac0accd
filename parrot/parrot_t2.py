import yaml
import pandas as pd
import torch


from models.parrot_model import ParrotConditionPredictionModel
from preprocess_script.uspto_script.utils import canonicalize_smiles
from models.utils import caonicalize_rxn_smiles, get_output_results, inference_load



def format_beam_predictions(test_df, beam_output):
    rows = []
    for idx, beams in enumerate(beam_output):
        rxn = test_df.loc[idx, "rxn_smiles"]
        for rank, (beam_entry, score) in enumerate(beams, start=1):
            row = {
                "rxn_smiles": rxn,
                "rank": rank,
                "parrot_score": round(score, 4),
                "catalyst1": beam_entry[0],
                "solvent1": beam_entry[1],
                "solvent2": beam_entry[2],
                "reagent1": beam_entry[3],
                "reagent2": beam_entry[4],
            }
            rows.append(row)

    df = pd.DataFrame(rows)
    
    # Optional: add joined reagents column
    df["Reagents"] = df[["catalyst1", "solvent1", "solvent2", "reagent1", "reagent2"]].apply(
        lambda row: ".".join(filter(None, row)), axis=1
    )
    
    return df
    

# === INITIALIZE EVERYTHING ONCE === #
def initialize_model_and_config(config_path: str, gpu: int = 0):
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)

    model_args = config['model_args']
    dataset_args = config['dataset_args']
    inference_args = config['inference_args']

    label_mapping = inference_load(**dataset_args)
    model_args['decoder_args'].update({
        'condition_label_mapping': label_mapping,
        'tgt_vocab_size': len(label_mapping[0]),
        'dataloader_num_workers': 0
    })

    model_args['use_temperature'] = dataset_args.get('use_temperature', False)
    print('Using Temperature:', model_args['use_temperature'])

    model = ParrotConditionPredictionModel(
        "bert",
        model_args['best_model_dir'],
        args=model_args,
        use_cuda=gpu >= 0,
        cuda_device=gpu
    )

    return model, model_args, inference_args


# === INFERENCE FUNCTION === #
def run_inference_from_list(
    input_rxn_list,
    n_best = 1,
    batch_size: int = 8
):

    model, model_args, inference_args = initialize_model_and_config('configs/config_inference_use_uspto.yaml', gpu=-1)

    if not input_rxn_list:
        return None

    # Prepare input dataframe
    test_df = pd.DataFrame({
        'rxn_smiles': input_rxn_list,
        'text': input_rxn_list,
        'labels': [[0] * (8 if model_args['use_temperature'] else 7)] * len(input_rxn_list)
    })

    # Canonicalize reaction SMILES
    print('Canonicalizing input reaction SMILES...')
    test_df['text'] = test_df.text.apply(caonicalize_rxn_smiles)
    test_df = test_df[test_df['text'] != ''].reset_index(drop=True)

    if test_df.empty:
        print("No valid input SMILES after canonicalization.")
        return None

    # Run inference
    beam = inference_args['beam']
    pred_conditions, pred_temperatures = model.condition_beam_search(
        test_df,
        output_dir=model_args['best_model_dir'],
        beam=beam,
        test_batch_size=batch_size,
        n_best = n_best,
        calculate_topk_accuracy=False
    )

    return format_beam_predictions(test_df, pred_conditions)