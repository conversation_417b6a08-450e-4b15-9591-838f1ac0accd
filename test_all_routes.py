#!/usr/bin/env python3
"""
Test script to verify we're processing all routes correctly
"""

from modern_visualizer import ModernSynthesisVisualizer

def test_all_routes_processing():
    """Test that we process all routes from the database."""
    
    print("🔍 TESTING ALL ROUTES PROCESSING")
    print("=" * 40)
    
    visualizer = ModernSynthesisVisualizer()
    
    # Connect to MongoDB
    if not visualizer.connect_to_mongodb():
        print("❌ Failed to connect to MongoDB")
        return False
    
    # Test with your actual request ID
    request_id = "os6df8a82-9fae-479e-ba10-e92e0f8f9492"
    routes = visualizer.get_route_data(request_id)
    
    if not routes:
        print(f"❌ No routes found for request_id: {request_id}")
        
        # Try to find any routes in the database
        try:
            sample_routes = list(visualizer.collection.find().limit(5))
            if sample_routes:
                print(f"\n💡 Found {len(sample_routes)} sample routes in database:")
                for route in sample_routes:
                    print(f"   Request ID: {route.get('request_id', 'N/A')}")
                    print(f"   Route ID: {route.get('route_id', 'N/A')}")
                    print(f"   Target: {route.get('target_smiles', 'N/A')}")
                    print(f"   Steps: {route.get('num_steps', 'N/A')}")
                    print()
                
                # Test with the first available route
                test_request_id = sample_routes[0].get('request_id')
                if test_request_id:
                    print(f"🧪 Testing with available request ID: {test_request_id}")
                    routes = visualizer.get_route_data(test_request_id)
        except Exception as e:
            print(f"❌ Error querying database: {e}")
            return False
    
    if not routes:
        print("❌ Still no routes found")
        return False
    
    print(f"✅ Found {len(routes)} routes")
    
    # Analyze the routes
    print(f"\n📊 ROUTE ANALYSIS:")
    total_reactions = 0
    route_details = []
    
    for i, route in enumerate(routes):
        route_id = route.get('route_id', f'route_{i}')
        target_smiles = route.get('target_smiles', 'Unknown')
        num_steps = route.get('num_steps', 0)
        reactions = route.get('data', [])
        
        route_details.append({
            'route_id': route_id,
            'target': target_smiles,
            'num_steps': num_steps,
            'reactions': len(reactions)
        })
        
        total_reactions += len(reactions)
        
        print(f"   Route {route_id}: {target_smiles} ({len(reactions)} reactions)")
    
    print(f"\n📈 TOTALS:")
    print(f"   Total routes: {len(routes)}")
    print(f"   Total reactions: {total_reactions}")
    
    # Test data transformation
    print(f"\n🔄 TESTING DATA TRANSFORMATION:")
    try:
        graph_data = visualizer.transform_data_for_d3(routes)
        
        print(f"✅ Transformation successful:")
        print(f"   Nodes: {len(graph_data['nodes'])}")
        print(f"   Links: {len(graph_data['links'])}")
        
        # Analyze node types
        node_types = {}
        for node in graph_data['nodes']:
            node_type = node.get('type', 'unknown')
            node_types[node_type] = node_types.get(node_type, 0) + 1
        
        print(f"   Node types: {node_types}")
        
        # Show some sample nodes
        print(f"\n🔍 SAMPLE NODES:")
        for node_type in ['target', 'reaction', 'terminal', 'intermediate']:
            sample_nodes = [n for n in graph_data['nodes'] if n.get('type') == node_type]
            if sample_nodes:
                sample = sample_nodes[0]
                print(f"   {node_type}: {sample.get('id', 'N/A')} - {sample.get('label', 'N/A')}")
        
        # Show some sample links
        print(f"\n🔗 SAMPLE LINKS:")
        link_types = {}
        for link in graph_data['links']:
            link_type = link.get('type', 'unknown')
            link_types[link_type] = link_types.get(link_type, 0) + 1
        
        print(f"   Link types: {link_types}")
        
        for link_type in ['synthesis', 'dependency', 'requires', 'produces']:
            sample_links = [l for l in graph_data['links'] if l.get('type') == link_type]
            if sample_links:
                sample = sample_links[0]
                print(f"   {link_type}: {sample.get('source', 'N/A')} → {sample.get('target', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Transformation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    success = test_all_routes_processing()
    
    if success:
        print(f"\n🎉 ALL ROUTES TEST PASSED!")
        print(f"The visualizer should now show all your routes and reactions.")
        print(f"\n🚀 Run the visualizer:")
        print(f"   python run_modern_visualizer.py")
        print(f"   Enter your request ID to see all 96+ reactions!")
    else:
        print(f"\n❌ TEST FAILED!")
        print(f"Check the error messages above.")

if __name__ == "__main__":
    main()
